<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Widget Example</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        
        .example-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .code-block {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Domain Widget Example</h1>
    
    <div class="example-section">
        <h2>How to Use</h2>
        <p>Add the <code>data-widget-id</code> attribute to any input field to enable domain enrichment:</p>
        <div class="code-block">
&lt;input type="text" data-widget-id="domain-enricher" placeholder="Enter domain (e.g., google.com)" /&gt;
        </div>
    </div>

    <div class="example-section">
        <h2>Live Examples</h2>
        
        <div class="form-group">
            <label for="domain1">Domain Input 1:</label>
            <input 
                type="text" 
                id="domain1"
                data-widget-id="domain-enricher-1" 
                placeholder="Enter domain (e.g., google.com)"
            />
        </div>
        
        <div class="form-group">
            <label for="domain2">Domain Input 2:</label>
            <input 
                type="text" 
                id="domain2"
                data-widget-id="domain-enricher-2" 
                placeholder="Try another domain (e.g., github.com)"
            />
        </div>
        
        <div class="form-group">
            <label for="domain3">Domain Input 3:</label>
            <input 
                type="text" 
                id="domain3"
                data-widget-id="domain-enricher-3" 
                placeholder="And another one (e.g., stackoverflow.com)"
            />
        </div>
    </div>

    <div class="example-section">
        <h2>Features</h2>
        <ul>
            <li><strong>Auto-initialization:</strong> Widgets are automatically initialized when the page loads</li>
            <li><strong>Debounced API calls:</strong> API is called 1.5 seconds after you stop typing</li>
            <li><strong>Blur trigger:</strong> API is also called when you click outside the input</li>
            <li><strong>Enter key trigger:</strong> Press Enter to immediately trigger the API call</li>
            <li><strong>Loading states:</strong> Shows a spinner while loading</li>
            <li><strong>Error handling:</strong> Displays errors if the API call fails</li>
            <li><strong>Positioned overlay:</strong> The response appears below the input field</li>
        </ul>
    </div>

    <div class="example-section">
        <h2>Manual Initialization</h2>
        <p>You can also manually initialize domain widgets after adding new inputs to the DOM:</p>
        <div class="code-block">
// Initialize all domain widgets on the page
window.Widget.initDomainWidgets();
        </div>
        
        <button onclick="addNewInput()">Add New Input Dynamically</button>
        <div id="dynamic-inputs"></div>
    </div>

    <!-- Load the widget script -->
    <script src="./dist/embed.js" data-api-url="http://localhost:3000" data-widget-id="test-widget"></script>
    
    <script>
        // Example of adding inputs dynamically
        function addNewInput() {
            const container = document.getElementById('dynamic-inputs');
            const inputCount = container.children.length + 1;
            
            const div = document.createElement('div');
            div.className = 'form-group';
            div.innerHTML = `
                <label>Dynamic Domain Input ${inputCount}:</label>
                <input 
                    type="text" 
                    data-widget-id="dynamic-${inputCount}" 
                    placeholder="Enter domain for dynamic input ${inputCount}"
                />
            `;
            
            container.appendChild(div);
            
            // Re-initialize domain widgets to pick up the new input
            if (window.Widget && window.Widget.initDomainWidgets) {
                window.Widget.initDomainWidgets();
            }
        }
        
        // Example of programmatic initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded. Domain widgets should be initialized automatically.');
            
            // You can also manually trigger initialization
            setTimeout(() => {
                if (window.Widget && window.Widget.initDomainWidgets) {
                    console.log('Manual initialization available via window.Widget.initDomainWidgets()');
                }
            }, 1000);
        });
    </script>
</body>
</html>
