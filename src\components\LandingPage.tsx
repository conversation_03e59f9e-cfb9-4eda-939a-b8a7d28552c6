import { useState } from "preact/compat";
import EnrichedInput, { createDomainInput } from "./EnrichedInput";

export default function LandingPage() {
  const [enrichmentData, setEnrichmentData] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const [domain, setDomain] = useState<string>("");

  const handleDomainEnriched = (data: any) => {
    console.log("Domain enriched:", data);
    setEnrichmentData(data);
    setError(""); // Clear any previous errors
  };

  const handleError = (errorMessage: string) => {
    console.error("Enrichment error:", errorMessage);
    setError(errorMessage);
    setEnrichmentData(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Domain Intelligence Platform
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Get instant insights about any domain. Just type and we'll enrich it automatically.
          </p>
        </div>

        {/* Main Input Section */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <label htmlFor="domain-input" className="block text-lg font-medium text-gray-700 mb-4">
              Enter Domain to Analyze:
            </label>
            
            {/* This is the main enriched input */}
            <EnrichedInput
              id="domain-input"
              value={domain}
              onChange={setDomain}
              onDomainEnriched={handleDomainEnriched}
              onError={handleError}
              placeholder="Enter domain (e.g., google.com, github.com)"
              className="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200"
            />
            
            <p className="text-sm text-gray-500 mt-2">
              Domain enrichment happens automatically as you type or when you press Enter.
            </p>
          </div>

          {/* Results Section */}
          {(enrichmentData || error) && (
            <div className="mt-8 bg-white rounded-xl shadow-lg p-8">
              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
                  <p className="text-red-700">{error}</p>
                </div>
              )}

              {enrichmentData && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    Enrichment Results for: <span className="text-blue-600">{domain}</span>
                  </h3>
                  <div className="bg-gray-50 rounded-lg p-4 overflow-auto">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(enrichmentData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Additional Examples */}
        <div className="mt-16 max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            More Examples
          </h2>
          
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Domain:
              </label>
              {createDomainInput({
                placeholder: "Enter company domain",
                onDomainEnriched: (data) => console.log("Company data:", data)
              })}
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Competitor Analysis:
              </label>
              {createDomainInput({
                placeholder: "Enter competitor domain",
                onDomainEnriched: (data) => console.log("Competitor data:", data)
              })}
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Regular Input (No Enrichment):
              </label>
              <input
                type="text"
                placeholder="This input doesn't have domain enrichment"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 outline-none transition-colors"
              />
            </div>
          </div>
        </div>

        {/* Info Section */}
        <div className="mt-16 bg-blue-50 rounded-xl p-8">
          <h3 className="text-xl font-semibold text-blue-900 mb-4">How It Works</h3>
          <ul className="text-blue-800 space-y-2">
            <li>• Only inputs with the wizard ID get automatic domain enrichment</li>
            <li>• Enrichment triggers 2 seconds after you stop typing, on Enter, or on blur</li>
            <li>• All API calls happen in the background - no visible widget UI</li>
            <li>• Results are passed to your callback functions for custom handling</li>
            <li>• Loading indicator appears in the input while processing</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
