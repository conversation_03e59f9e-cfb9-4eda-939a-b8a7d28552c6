export default function LandingPage() {
  const widgetId = import.meta.env.VITE_WIDGET_ID || 'cmdoay97t000j1fg1070b754d';

  return (
    <div className="p-8 space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">Domain Input (with widget):</label>
        <input
          type="text"
          data-widget-id={widgetId}
          placeholder="Enter domain"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          id="domain-input"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Regular Input (no widget):</label>
        <input
          type="text"
          placeholder="Regular input (no widget)"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        />
      </div>
    </div>
  );
}
