import { useEffect, useState } from "preact/compat";
import clsx from "clsx";
import WidgetComponent from "./WidgetComponent";
import { WidgetDataDto, WidgetDto } from "../dtos/WidgetDto";
import XIcon from "~/components/ui/icons/XIcon";

interface Props {
  widgetId: string;
  apiUrl: string;
  disabled?: boolean;
  options?: {
    isPreview?: boolean;
    openDelay?: number;
  };
  verbose?: boolean;
}

export default function Bubble({ widgetId, apiUrl, disabled, options, verbose }: Props) {
  const [loading, setLoading] = useState<boolean>(true);
  const [widget, setWidget] = useState<WidgetDto | null>(null);
  const [error, setError] = useState<string>();
  const [widgetData, setWidgetData] = useState<WidgetDataDto>();

  const currentPath = window.location.pathname;

  useEffect(() => {
    function fetchWidget() {
      let url = `${apiUrl}/api/widget/${widgetId}`;
      setError(undefined);
      fetch(url)
        .then(async (response) => {
          const data = await response.json();
          console.log({ data });
          setWidget(data);
          setLoading(false);
        })
        .catch((e: any) => {
          if (verbose) {
            // eslint-disable-next-line no-console
            console.error("[SaasRock.Widget] Error loading widget", {
              widgetId,
              url,
              error: e.message,
            });
          }
          setError(error);
        });
    }

    fetchWidget();
  }, [apiUrl, widgetId]);

  // Remove test widget functionality - now focused on domain enrichment only

  const onReset = () => {
    setWidgetData(undefined);
  };

  if (loading || !widget) {
    return null;
  }

  if (widget.appearance.hiddenInUrls.length > 0) {
    const hidden = widget.appearance.hiddenInUrls.find((url) => currentPath.startsWith(url));
    if (hidden) {
      if (verbose) {
        // eslint-disable-next-line no-console
        console.log("[SaasRock.Widget] Hidden in URL", {
          hiddenUrl: hidden,
          currentPath,
        });
      }
      return null;
    }
  }
  if (widget.appearance.visibleInUrls.length > 0) {
    const visible = widget.appearance.visibleInUrls.some((url) => currentPath.startsWith(url));
    if (!visible) {
      if (verbose) {
        // eslint-disable-next-line no-console
        console.log("[SaasRock.Widget] Not visible in URL", {
          visibleUrls: widget.appearance.visibleInUrls,
          currentPath,
        });
      }
      return null;
    }
  }

  if (!widget) {
    return null;
  }

  return (
    <BubbleButton
      widget={widget}
      widgetData={widgetData}
      disabled={disabled === true}
      onReset={!disabled ? onReset : undefined}
      options={options}
      error={error}
    />
  );
}

function BubbleButton({
  widget,
  widgetData,
  disabled,
  onReset,
  options,
  error,
}: {
  widget: WidgetDto;
  widgetData: WidgetDataDto | undefined;
  disabled: boolean;
  onReset?: () => void;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left" | "center";
  options?: {
    isPreview?: boolean;
    openDelay?: number;
  };
  error?: string;
}) {
  const [closed, setClosed] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    if (options?.openDelay !== undefined && options.openDelay >= 0) {
      setTimeout(() => {
        setOpen(true);
      }, options.openDelay);
    }
  }, [options?.openDelay]);

  if (closed) {
    return null;
  }
  return (
    <div className={clsx("z-50", !options?.isPreview && "fixed")}>
      <button
        type="button"
        disabled={disabled}
        className={clsx(
          !options?.isPreview && "fixed",
          widget.appearance.position === "bottom-right" && "bottom-6 right-6",
          widget.appearance.position === "bottom-left" && "bottom-6 left-6",
          widget.appearance.position === "top-right" && "right-6 top-6",
          widget.appearance.position === "top-left" && "left-6 top-6",
          widget.appearance.position === "center" && "bottom-6 left-6 right-6",
          `theme-${widget.appearance.theme}`,
          widget.appearance.mode === "dark" && "dark",
          "bg-primary text-primary-foreground hover:bg-primary/90 shadow",
          "rounded-full shadow-md",
          "flex h-14 w-14 items-center justify-center transition-all duration-300 ease-in-out"
        )}
        onClick={() => setOpen(!open)}
      >
        {widget.appearance.logo && !open ? (
          <img src={widget.appearance.logo} alt={widget.name} className="h-14 w-14 object-contain" />
        ) : (
          <>
            {!open ? (
              <svg className="h-7 w-7 transition-all duration-300 ease-in-out" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  fill-rule="evenodd"
                  d="M12 1.5a.75.75 0 0 1 .75.75V4.5a.75.75 0 0 1-1.5 0V2.25A.75.75 0 0 1 12 1.5ZM5.636 4.136a.75.75 0 0 1 1.06 0l1.592 1.591a.75.75 0 0 1-1.061 1.06l-1.591-1.59a.75.75 0 0 1 0-1.061Zm12.728 0a.75.75 0 0 1 0 1.06l-1.591 1.592a.75.75 0 0 1-1.06-1.061l1.59-1.591a.75.75 0 0 1 1.061 0Zm-6.816 4.496a.75.75 0 0 1 .82.311l5.228 7.917a.75.75 0 0 1-.777 1.148l-2.097-.43 1.045 3.9a.75.75 0 0 1-1.45.388l-1.044-3.899-1.601 1.42a.75.75 0 0 1-1.247-.606l.569-9.47a.75.75 0 0 1 .554-.68ZM3 10.5a.75.75 0 0 1 .75-.75H6a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 10.5Zm14.25 0a.75.75 0 0 1 .75-.75h2.25a.75.75 0 0 1 0 1.5H18a.75.75 0 0 1-.75-.75Zm-8.962 3.712a.75.75 0 0 1 0 1.061l-1.591 1.591a.75.75 0 1 1-1.061-1.06l1.591-1.592a.75.75 0 0 1 1.06 0Z"
                  clip-rule="evenodd"
                />
              </svg>
            ) : (
              <XIcon className="h-5 w-5 transition-all duration-300 ease-in-out" />
            )}
          </>
        )}
      </button>
      {open && (
        <div
          className={clsx(
            "fixed",
            widget.appearance.mode === "dark" && "dark",
            widget.appearance.position === "bottom-right" && " bottom-24 right-6",
            widget.appearance.position === "bottom-left" && "bottom-24 left-6",
            widget.appearance.position === "top-right" && "right-6 top-24",
            widget.appearance.position === "top-left" && "left-6 top-24",
            widget.appearance.position === "center" && "left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform",
            "border-border flex w-80 flex-col overflow-hidden rounded-lg border shadow-lg",
            "transition-all duration-700 ease-in-out",
            "h-[calc(100vh-200px)]"
          )}
        >
          <WidgetComponent
            className="h-[calc(100vh-200px)]"
            widget={widget}
            widgetData={widgetData}
            disabled={disabled || options?.isPreview}
            onReset={onReset}
            size="sm"
            onClose={() => setClosed(true)}
            error={error}
          />
        </div>
      )}
    </div>
  );
}
