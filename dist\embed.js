(function(W){typeof define=="function"&&define.amd?define(W):W()})(function(){"use strict";var Rr;var W,b,Ee,O,Re,je,me,_e,he,be,Z={},Ie=[],Tr=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,oe=Array.isArray;function E(e,r){for(var t in r)e[t]=r[t];return e}function We(e){var r=e.parentNode;r&&r.removeChild(e)}function ve(e,r,t){var o,i,n,a={};for(n in r)n=="key"?o=r[n]:n=="ref"?i=r[n]:a[n]=r[n];if(arguments.length>2&&(a.children=arguments.length>3?W.call(arguments,2):t),typeof e=="function"&&e.defaultProps!=null)for(n in e.defaultProps)a[n]===void 0&&(a[n]=e.defaultProps[n]);return q(e,a,o,i,null)}function q(e,r,t,o,i){var n={type:e,props:r,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:i??++Ee,__i:-1,__u:0};return i==null&&b.vnode!=null&&b.vnode(n),n}function U(e){return e.children}function R(e,r){this.props=e,this.context=r}function V(e,r){if(r==null)return e.__?V(e.__,e.__i+1):null;for(var t;r<e.__k.length;r++)if((t=e.__k[r])!=null&&t.__e!=null)return t.__e;return typeof e.type=="function"?V(e):null}function Ue(e){var r,t;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,r=0;r<e.__k.length;r++)if((t=e.__k[r])!=null&&t.__e!=null){e.__e=e.__c.base=t.__e;break}return Ue(e)}}function Te(e){(!e.__d&&(e.__d=!0)&&O.push(e)&&!ne.__r++||Re!==b.debounceRendering)&&((Re=b.debounceRendering)||je)(ne)}function ne(){var e,r,t,o,i,n,a,l;for(O.sort(me);e=O.shift();)e.__d&&(r=O.length,o=void 0,n=(i=(t=e).__v).__e,a=[],l=[],t.__P&&((o=E({},i)).__v=i.__v+1,b.vnode&&b.vnode(o),ye(t.__P,o,i,t.__n,t.__P.namespaceURI,32&i.__u?[n]:null,a,n??V(i),!!(32&i.__u),l),o.__v=i.__v,o.__.__k[o.__i]=o,Be(a,o,l),o.__e!=n&&Ue(o)),O.length>r&&O.sort(me));ne.__r=0}function Le(e,r,t,o,i,n,a,l,c,d,u){var s,g,f,w,y,_=o&&o.__k||Ie,m=r.length;for(t.__d=c,Lr(t,r,_),c=t.__d,s=0;s<m;s++)(f=t.__k[s])!=null&&typeof f!="boolean"&&typeof f!="function"&&(g=f.__i===-1?Z:_[f.__i]||Z,f.__i=s,ye(e,f,g,i,n,a,l,c,d,u),w=f.__e,f.ref&&g.ref!=f.ref&&(g.ref&&we(g.ref,null,f),u.push(f.ref,f.__c||w,f)),y==null&&w!=null&&(y=w),65536&f.__u||g.__k===f.__k?(c&&!c.isConnected&&(c=V(g)),c=He(f,c,e)):typeof f.type=="function"&&f.__d!==void 0?c=f.__d:w&&(c=w.nextSibling),f.__d=void 0,f.__u&=-196609);t.__d=c,t.__e=y}function Lr(e,r,t){var o,i,n,a,l,c=r.length,d=t.length,u=d,s=0;for(e.__k=[],o=0;o<c;o++)a=o+s,(i=e.__k[o]=(i=r[o])==null||typeof i=="boolean"||typeof i=="function"?null:typeof i=="string"||typeof i=="number"||typeof i=="bigint"||i.constructor==String?q(null,i,null,null,null):oe(i)?q(U,{children:i},null,null,null):i.constructor===void 0&&i.__b>0?q(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i)!=null?(i.__=e,i.__b=e.__b+1,l=Hr(i,t,a,u),i.__i=l,n=null,l!==-1&&(u--,(n=t[l])&&(n.__u|=131072)),n==null||n.__v===null?(l==-1&&s--,typeof i.type!="function"&&(i.__u|=65536)):l!==a&&(l===a+1?s++:l>a?u>c-a?s+=l-a:s--:l<a?l==a-1&&(s=l-a):s=0,l!==o+s&&(i.__u|=65536))):(n=t[a])&&n.key==null&&n.__e&&!(131072&n.__u)&&(n.__e==e.__d&&(e.__d=V(n)),xe(n,n,!1),t[a]=null,u--);if(u)for(o=0;o<d;o++)(n=t[o])!=null&&!(131072&n.__u)&&(n.__e==e.__d&&(e.__d=V(n)),xe(n,n))}function He(e,r,t){var o,i;if(typeof e.type=="function"){for(o=e.__k,i=0;o&&i<o.length;i++)o[i]&&(o[i].__=e,r=He(o[i],r,t));return r}e.__e!=r&&(t.insertBefore(e.__e,r||null),r=e.__e);do r=r&&r.nextSibling;while(r!=null&&r.nodeType===8);return r}function j(e,r){return r=r||[],e==null||typeof e=="boolean"||(oe(e)?e.some(function(t){j(t,r)}):r.push(e)),r}function Hr(e,r,t,o){var i=e.key,n=e.type,a=t-1,l=t+1,c=r[t];if(c===null||c&&i==c.key&&n===c.type&&!(131072&c.__u))return t;if(o>(c!=null&&!(131072&c.__u)?1:0))for(;a>=0||l<r.length;){if(a>=0){if((c=r[a])&&!(131072&c.__u)&&i==c.key&&n===c.type)return a;a--}if(l<r.length){if((c=r[l])&&!(131072&c.__u)&&i==c.key&&n===c.type)return l;l++}}return-1}function Oe(e,r,t){r[0]==="-"?e.setProperty(r,t??""):e[r]=t==null?"":typeof t!="number"||Tr.test(r)?t:t+"px"}function ie(e,r,t,o,i){var n;e:if(r==="style")if(typeof t=="string")e.style.cssText=t;else{if(typeof o=="string"&&(e.style.cssText=o=""),o)for(r in o)t&&r in t||Oe(e.style,r,"");if(t)for(r in t)o&&t[r]===o[r]||Oe(e.style,r,t[r])}else if(r[0]==="o"&&r[1]==="n")n=r!==(r=r.replace(/(PointerCapture)$|Capture$/i,"$1")),r=r.toLowerCase()in e||r==="onFocusOut"||r==="onFocusIn"?r.toLowerCase().slice(2):r.slice(2),e.l||(e.l={}),e.l[r+n]=t,t?o?t.u=o.u:(t.u=_e,e.addEventListener(r,n?be:he,n)):e.removeEventListener(r,n?be:he,n);else{if(i=="http://www.w3.org/2000/svg")r=r.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(r!="width"&&r!="height"&&r!="href"&&r!="list"&&r!="form"&&r!="tabIndex"&&r!="download"&&r!="rowSpan"&&r!="colSpan"&&r!="role"&&r in e)try{e[r]=t??"";break e}catch{}typeof t=="function"||(t==null||t===!1&&r[4]!=="-"?e.removeAttribute(r):e.setAttribute(r,t))}}function Ve(e){return function(r){if(this.l){var t=this.l[r.type+e];if(r.t==null)r.t=_e++;else if(r.t<t.u)return;return t(b.event?b.event(r):r)}}}function ye(e,r,t,o,i,n,a,l,c,d){var u,s,g,f,w,y,_,m,v,C,z,$,ee,F,G,P=r.type;if(r.constructor!==void 0)return null;128&t.__u&&(c=!!(32&t.__u),n=[l=r.__e=t.__e]),(u=b.__b)&&u(r);e:if(typeof P=="function")try{if(m=r.props,v=(u=P.contextType)&&o[u.__c],C=u?v?v.props.value:u.__:o,t.__c?_=(s=r.__c=t.__c).__=s.__E:("prototype"in P&&P.prototype.render?r.__c=s=new P(m,C):(r.__c=s=new R(m,C),s.constructor=P,s.render=Vr),v&&v.sub(s),s.props=m,s.state||(s.state={}),s.context=C,s.__n=o,g=s.__d=!0,s.__h=[],s._sb=[]),s.__s==null&&(s.__s=s.state),P.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=E({},s.__s)),E(s.__s,P.getDerivedStateFromProps(m,s.__s))),f=s.props,w=s.state,s.__v=r,g)P.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(P.getDerivedStateFromProps==null&&m!==f&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(m,C),!s.__e&&(s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(m,s.__s,C)===!1||r.__v===t.__v)){for(r.__v!==t.__v&&(s.props=m,s.state=s.__s,s.__d=!1),r.__e=t.__e,r.__k=t.__k,r.__k.forEach(function(H){H&&(H.__=r)}),z=0;z<s._sb.length;z++)s.__h.push(s._sb[z]);s._sb=[],s.__h.length&&a.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(m,s.__s,C),s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(f,w,y)})}if(s.context=C,s.props=m,s.__P=e,s.__e=!1,$=b.__r,ee=0,"prototype"in P&&P.prototype.render){for(s.state=s.__s,s.__d=!1,$&&$(r),u=s.render(s.props,s.state,s.context),F=0;F<s._sb.length;F++)s.__h.push(s._sb[F]);s._sb=[]}else do s.__d=!1,$&&$(r),u=s.render(s.props,s.state,s.context),s.state=s.__s;while(s.__d&&++ee<25);s.state=s.__s,s.getChildContext!=null&&(o=E(E({},o),s.getChildContext())),g||s.getSnapshotBeforeUpdate==null||(y=s.getSnapshotBeforeUpdate(f,w)),Le(e,oe(G=u!=null&&u.type===U&&u.key==null?u.props.children:u)?G:[G],r,t,o,i,n,a,l,c,d),s.base=r.__e,r.__u&=-161,s.__h.length&&a.push(s),_&&(s.__E=s.__=null)}catch(H){r.__v=null,c||n!=null?(r.__e=l,r.__u|=c?160:32,n[n.indexOf(l)]=null):(r.__e=t.__e,r.__k=t.__k),b.__e(H,r,t)}else n==null&&r.__v===t.__v?(r.__k=t.__k,r.__e=t.__e):r.__e=Or(t.__e,r,t,o,i,n,a,c,d);(u=b.diffed)&&u(r)}function Be(e,r,t){r.__d=void 0;for(var o=0;o<t.length;o++)we(t[o],t[++o],t[++o]);b.__c&&b.__c(r,e),e.some(function(i){try{e=i.__h,i.__h=[],e.some(function(n){n.call(i)})}catch(n){b.__e(n,i.__v)}})}function Or(e,r,t,o,i,n,a,l,c){var d,u,s,g,f,w,y,_=t.props,m=r.props,v=r.type;if(v==="svg"?i="http://www.w3.org/2000/svg":v==="math"?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),n!=null){for(d=0;d<n.length;d++)if((f=n[d])&&"setAttribute"in f==!!v&&(v?f.localName===v:f.nodeType===3)){e=f,n[d]=null;break}}if(e==null){if(v===null)return document.createTextNode(m);e=document.createElementNS(i,v,m.is&&m),n=null,l=!1}if(v===null)_===m||l&&e.data===m||(e.data=m);else{if(n=n&&W.call(e.childNodes),_=t.props||Z,!l&&n!=null)for(_={},d=0;d<e.attributes.length;d++)_[(f=e.attributes[d]).name]=f.value;for(d in _)if(f=_[d],d!="children"){if(d=="dangerouslySetInnerHTML")s=f;else if(d!=="key"&&!(d in m)){if(d=="value"&&"defaultValue"in m||d=="checked"&&"defaultChecked"in m)continue;ie(e,d,null,f,i)}}for(d in m)f=m[d],d=="children"?g=f:d=="dangerouslySetInnerHTML"?u=f:d=="value"?w=f:d=="checked"?y=f:d==="key"||l&&typeof f!="function"||_[d]===f||ie(e,d,f,_[d],i);if(u)l||s&&(u.__html===s.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),r.__k=[];else if(s&&(e.innerHTML=""),Le(e,oe(g)?g:[g],r,t,o,v==="foreignObject"?"http://www.w3.org/1999/xhtml":i,n,a,n?n[0]:t.__k&&V(t,0),l,c),n!=null)for(d=n.length;d--;)n[d]!=null&&We(n[d]);l||(d="value",w!==void 0&&(w!==e[d]||v==="progress"&&!w||v==="option"&&w!==_[d])&&ie(e,d,w,_[d],i),d="checked",y!==void 0&&y!==e[d]&&ie(e,d,y,_[d],i))}return e}function we(e,r,t){try{typeof e=="function"?e(r):e.current=r}catch(o){b.__e(o,t)}}function xe(e,r,t){var o,i;if(b.unmount&&b.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||we(o,null,r)),(o=e.__c)!=null){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(n){b.__e(n,r)}o.base=o.__P=null}if(o=e.__k)for(i=0;i<o.length;i++)o[i]&&xe(o[i],r,t||typeof e.type!="function");t||e.__e==null||We(e.__e),e.__c=e.__=e.__e=e.__d=void 0}function Vr(e,r,t){return this.constructor(e,t)}function Br(e,r,t){var o,i,n,a;b.__&&b.__(e,r),i=(o=typeof t=="function")?null:r.__k,n=[],a=[],ye(r,e=(!o&&t||r).__k=ve(U,null,[e]),i||Z,Z,r.namespaceURI,!o&&t?[t]:i?null:r.firstChild?W.call(r.childNodes):null,n,!o&&t?t:i?i.__e:r.firstChild,o,a),Be(n,e,a)}function Fr(e,r,t){var o,i,n,a,l=E({},e.props);for(n in e.type&&e.type.defaultProps&&(a=e.type.defaultProps),r)n=="key"?o=r[n]:n=="ref"?i=r[n]:l[n]=r[n]===void 0&&a!==void 0?a[n]:r[n];return arguments.length>2&&(l.children=arguments.length>3?W.call(arguments,2):t),q(e.type,l,o||e.key,i||e.ref,null)}W=Ie.slice,b={__e:function(e,r,t,o){for(var i,n,a;r=r.__;)if((i=r.__c)&&!i.__)try{if((n=i.constructor)&&n.getDerivedStateFromError!=null&&(i.setState(n.getDerivedStateFromError(e)),a=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(e,o||{}),a=i.__d),a)return i.__E=i}catch(l){e=l}throw e}},Ee=0,R.prototype.setState=function(e,r){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=E({},this.state),typeof e=="function"&&(e=e(E({},t),this.props)),e&&E(t,e),e!=null&&this.__v&&(r&&this._sb.push(r),Te(this))},R.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Te(this))},R.prototype.render=U,O=[],je=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,me=function(e,r){return e.__v.__b-r.__v.__b},ne.__r=0,_e=0,he=Ve(!1),be=Ve(!0);function Fe(e){var r,t,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(r=0;r<i;r++)e[r]&&(t=Fe(e[r]))&&(o&&(o+=" "),o+=t)}else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function A(){for(var e,r,t=0,o="",i=arguments.length;t<i;t++)(e=arguments[t])&&(r=Fe(e))&&(o&&(o+=" "),o+=r);return o}var Y,S,ke,De,ae=0,Ge=[],se=[],N=b,Ze=N.__b,qe=N.__r,Ye=N.diffed,Xe=N.__c,Je=N.unmount,Ke=N.__;function Ce(e,r){N.__h&&N.__h(S,e,ae||r),ae=0;var t=S.__H||(S.__H={__:[],__h:[]});return e>=t.__.length&&t.__.push({__V:se}),t.__[e]}function M(e){return ae=1,Dr(tr,e)}function Dr(e,r,t){var o=Ce(Y++,2);if(o.t=e,!o.__c&&(o.__=[t?t(r):tr(void 0,r),function(l){var c=o.__N?o.__N[0]:o.__[0],d=o.t(c,l);c!==d&&(o.__N=[d,o.__[1]],o.__c.setState({}))}],o.__c=S,!S.u)){var i=function(l,c,d){if(!o.__c.__H)return!0;var u=o.__c.__H.__.filter(function(g){return!!g.__c});if(u.every(function(g){return!g.__N}))return!n||n.call(this,l,c,d);var s=!1;return u.forEach(function(g){if(g.__N){var f=g.__[0];g.__=g.__N,g.__N=void 0,f!==g.__[0]&&(s=!0)}}),!(!s&&o.__c.props===l)&&(!n||n.call(this,l,c,d))};S.u=!0;var n=S.shouldComponentUpdate,a=S.componentWillUpdate;S.componentWillUpdate=function(l,c,d){if(this.__e){var u=n;n=void 0,i(l,c,d),n=u}a&&a.call(this,l,c,d)},S.shouldComponentUpdate=i}return o.__N||o.__}function Qe(e,r){var t=Ce(Y++,3);!N.__s&&rr(t.__H,r)&&(t.__=e,t.i=r,S.__H.__h.push(t))}function Gr(e){return ae=5,Zr(function(){return{current:e}},[])}function Zr(e,r){var t=Ce(Y++,7);return rr(t.__H,r)?(t.__V=e(),t.i=r,t.__h=e,t.__V):t.__}function qr(){for(var e;e=Ge.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(le),e.__H.__h.forEach(Se),e.__H.__h=[]}catch(r){e.__H.__h=[],N.__e(r,e.__v)}}N.__b=function(e){S=null,Ze&&Ze(e)},N.__=function(e,r){e&&r.__k&&r.__k.__m&&(e.__m=r.__k.__m),Ke&&Ke(e,r)},N.__r=function(e){qe&&qe(e),Y=0;var r=(S=e.__c).__H;r&&(ke===S?(r.__h=[],S.__h=[],r.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=se,t.__N=t.i=void 0})):(r.__h.forEach(le),r.__h.forEach(Se),r.__h=[],Y=0)),ke=S},N.diffed=function(e){Ye&&Ye(e);var r=e.__c;r&&r.__H&&(r.__H.__h.length&&(Ge.push(r)!==1&&De===N.requestAnimationFrame||((De=N.requestAnimationFrame)||Yr)(qr)),r.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==se&&(t.__=t.__V),t.i=void 0,t.__V=se})),ke=S=null},N.__c=function(e,r){r.some(function(t){try{t.__h.forEach(le),t.__h=t.__h.filter(function(o){return!o.__||Se(o)})}catch(o){r.some(function(i){i.__h&&(i.__h=[])}),r=[],N.__e(o,t.__v)}}),Xe&&Xe(e,r)},N.unmount=function(e){Je&&Je(e);var r,t=e.__c;t&&t.__H&&(t.__H.__.forEach(function(o){try{le(o)}catch(i){r=i}}),t.__H=void 0,r&&N.__e(r,t.__v))};var er=typeof requestAnimationFrame=="function";function Yr(e){var r,t=function(){clearTimeout(o),er&&cancelAnimationFrame(r),setTimeout(e)},o=setTimeout(t,100);er&&(r=requestAnimationFrame(t))}function le(e){var r=S,t=e.__c;typeof t=="function"&&(e.__c=void 0,t()),S=r}function Se(e){var r=S;e.__c=e.__(),S=r}function rr(e,r){return!e||e.length!==r.length||r.some(function(t,o){return t!==e[o]})}function tr(e,r){return typeof r=="function"?r(e):r}function or(e,r){for(var t in r)e[t]=r[t];return e}function nr(e,r){for(var t in e)if(t!=="__source"&&!(t in r))return!0;for(var o in r)if(o!=="__source"&&e[o]!==r[o])return!0;return!1}function ir(e,r){this.props=e,this.context=r}(ir.prototype=new R).isPureReactComponent=!0,ir.prototype.shouldComponentUpdate=function(e,r){return nr(this.props,e)||nr(this.state,r)};var ar=b.__b;b.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ar&&ar(e)};var Xr=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function de(e){function r(t){var o=or({},t);return delete o.ref,e(o,t.ref||null)}return r.$$typeof=Xr,r.render=r,r.prototype.isReactComponent=r.__f=!0,r.displayName="ForwardRef("+(e.displayName||e.name)+")",r}var sr=function(e,r){return e==null?null:j(j(e).map(r))},X={map:sr,forEach:sr,count:function(e){return e?j(e).length:0},only:function(e){var r=j(e);if(r.length!==1)throw"Children.only";return r[0]},toArray:j},Jr=b.__e;b.__e=function(e,r,t,o){if(e.then){for(var i,n=r;n=n.__;)if((i=n.__c)&&i.__c)return r.__e==null&&(r.__e=t.__e,r.__k=t.__k),i.__c(e,r)}Jr(e,r,t,o)};var lr=b.unmount;function dr(e,r,t){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(o){typeof o.__c=="function"&&o.__c()}),e.__c.__H=null),(e=or({},e)).__c!=null&&(e.__c.__P===t&&(e.__c.__P=r),e.__c=null),e.__k=e.__k&&e.__k.map(function(o){return dr(o,r,t)})),e}function cr(e,r,t){return e&&t&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(o){return cr(o,r,t)}),e.__c&&e.__c.__P===r&&(e.__e&&t.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=t)),e}function Ne(){this.__u=0,this.t=null,this.__b=null}function ur(e){var r=e.__.__c;return r&&r.__a&&r.__a(e)}function ce(){this.u=null,this.o=null}b.unmount=function(e){var r=e.__c;r&&r.__R&&r.__R(),r&&32&e.__u&&(e.type=null),lr&&lr(e)},(Ne.prototype=new R).__c=function(e,r){var t=r.__c,o=this;o.t==null&&(o.t=[]),o.t.push(t);var i=ur(o.__v),n=!1,a=function(){n||(n=!0,t.__R=null,i?i(l):l())};t.__R=a;var l=function(){if(!--o.__u){if(o.state.__a){var c=o.state.__a;o.__v.__k[0]=cr(c,c.__c.__P,c.__c.__O)}var d;for(o.setState({__a:o.__b=null});d=o.t.pop();)d.forceUpdate()}};o.__u++||32&r.__u||o.setState({__a:o.__b=o.__v.__k[0]}),e.then(a,a)},Ne.prototype.componentWillUnmount=function(){this.t=[]},Ne.prototype.render=function(e,r){if(this.__b){if(this.__v.__k){var t=document.createElement("div"),o=this.__v.__k[0].__c;this.__v.__k[0]=dr(this.__b,t,o.__O=o.__P)}this.__b=null}var i=r.__a&&ve(U,null,e.fallback);return i&&(i.__u&=-33),[ve(U,null,r.__a?null:e.children),i]};var fr=function(e,r,t){if(++t[1]===t[0]&&e.o.delete(r),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.o.size))for(t=e.u;t;){for(;t.length>3;)t.pop()();if(t[1]<t[0])break;e.u=t=t[2]}};(ce.prototype=new R).__a=function(e){var r=this,t=ur(r.__v),o=r.o.get(e);return o[0]++,function(i){var n=function(){r.props.revealOrder?(o.push(i),fr(r,e,o)):i()};t?t(n):n()}},ce.prototype.render=function(e){this.u=null,this.o=new Map;var r=j(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&r.reverse();for(var t=r.length;t--;)this.o.set(r[t],this.u=[1,0,this.u]);return e.children},ce.prototype.componentDidUpdate=ce.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(r,t){fr(e,t,r)})};var pr=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Kr=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Qr=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,et=/[A-Z0-9]/g,rt=typeof document<"u",tt=function(e){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(e)};R.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(R.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(r){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:r})}})});var gr=b.event;function ot(){}function nt(){return this.cancelBubble}function it(){return this.defaultPrevented}b.event=function(e){return gr&&(e=gr(e)),e.persist=ot,e.isPropagationStopped=nt,e.isDefaultPrevented=it,e.nativeEvent=e};var at={enumerable:!1,configurable:!0,get:function(){return this.class}},mr=b.vnode;b.vnode=function(e){typeof e.type=="string"&&function(r){var t=r.props,o=r.type,i={};for(var n in t){var a=t[n];if(!(n==="value"&&"defaultValue"in t&&a==null||rt&&n==="children"&&o==="noscript"||n==="class"||n==="className")){var l=n.toLowerCase();n==="defaultValue"&&"value"in t&&t.value==null?n="value":n==="download"&&a===!0?a="":l==="translate"&&a==="no"?a=!1:l==="ondoubleclick"?n="ondblclick":l!=="onchange"||o!=="input"&&o!=="textarea"||tt(t.type)?l==="onfocus"?n="onfocusin":l==="onblur"?n="onfocusout":Qr.test(n)?n=l:o.indexOf("-")===-1&&Kr.test(n)?n=n.replace(et,"-$&").toLowerCase():a===null&&(a=void 0):l=n="oninput",l==="oninput"&&i[n=l]&&(n="oninputCapture"),i[n]=a}}o=="select"&&i.multiple&&Array.isArray(i.value)&&(i.value=j(t.children).forEach(function(c){c.props.selected=i.value.indexOf(c.props.value)!=-1})),o=="select"&&i.defaultValue!=null&&(i.value=j(t.children).forEach(function(c){c.props.selected=i.multiple?i.defaultValue.indexOf(c.props.value)!=-1:i.defaultValue==c.props.value})),t.class&&!t.className?(i.class=t.class,Object.defineProperty(i,"className",at)):(t.className&&!t.class||t.class&&t.className)&&(i.class=i.className=t.className),r.props=i}(e),e.$$typeof=pr,mr&&mr(e)};var _r=b.__r;b.__r=function(e){_r&&_r(e),e.__c};var hr=b.diffed;b.diffed=function(e){hr&&hr(e);var r=e.props,t=e.__e;t!=null&&e.type==="textarea"&&"value"in r&&r.value!==t.value&&(t.value=r.value==null?"":r.value)};function J(e){return!!e&&e.$$typeof===pr}function br(e){return J(e)?Fr.apply(null,arguments):e}var st=0;function p(e,r,t,o,i,n){r||(r={});var a,l,c=r;if("ref"in c)for(l in c={},r)l=="ref"?a=r[l]:c[l]=r[l];var d={type:e,props:c,key:t,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--st,__i:-1,__u:0,__source:i,__self:n};if(typeof e=="function"&&(a=e.defaultProps))for(l in a)c[l]===void 0&&(c[l]=a[l]);return b.vnode&&b.vnode(d),d}function lt({title:e="Error",text:r="",children:t}){return p("div",{className:"not-prose rounded-md border border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900",children:p("div",{className:"rounded-md p-4",children:p("div",{className:"flex",children:[p("div",{className:"flex-shrink-0",children:p("svg",{className:"h-5 w-5 text-red-400 dark:text-red-300",viewBox:"0 0 20 20",fill:"currentColor",children:p("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),p("div",{className:"ml-3",children:[p("h3",{className:"text-sm font-medium leading-5 text-red-800 dark:text-red-100",children:e}),p("div",{className:"mt-2 text-sm leading-5 text-red-700 dark:text-red-300",children:p("div",{children:[r," ",t]})})]})]})})})}function dt(e,r){typeof e=="function"?e(r):e!=null&&(e.current=r)}function ct(...e){return r=>e.forEach(t=>dt(t,r))}var vr=de((e,r)=>{const{children:t,...o}=e,i=X.toArray(t),n=i.find(ft);if(n){const a=n.props.children,l=i.map(c=>c===n?X.count(a)>1?X.only(null):J(a)?a.props.children:null:c);return p(ze,{...o,ref:r,children:J(a)?br(a,void 0,l):null})}return p(ze,{...o,ref:r,children:t})});vr.displayName="Slot";var ze=de((e,r)=>{const{children:t,...o}=e;if(J(t)){const i=gt(t);return br(t,{...pt(o,t.props),ref:r?ct(r,i):i})}return X.count(t)>1?X.only(null):null});ze.displayName="SlotClone";var ut=({children:e})=>p(U,{children:e});function ft(e){return J(e)&&e.type===ut}function pt(e,r){const t={...r};for(const o in r){const i=e[o],n=r[o];/^on[A-Z]/.test(o)?i&&n?t[o]=(...l)=>{n(...l),i(...l)}:i&&(t[o]=i):o==="style"?t[o]={...i,...n}:o==="className"&&(t[o]=[i,n].filter(Boolean).join(" "))}return{...e,...t}}function gt(e){var o,i;let r=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(r=(i=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:i.get,t=r&&"isReactWarning"in r&&r.isReactWarning,t?e.props.ref:e.props.ref||e.ref)}function yr(e){var r,t,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e))for(r=0;r<e.length;r++)e[r]&&(t=yr(e[r]))&&(o&&(o+=" "),o+=t);else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function mt(){for(var e,r,t=0,o="";t<arguments.length;)(e=arguments[t++])&&(r=yr(e))&&(o&&(o+=" "),o+=r);return o}const wr=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,xr=mt,_t=(e,r)=>t=>{var o;if((r==null?void 0:r.variants)==null)return xr(e,t==null?void 0:t.class,t==null?void 0:t.className);const{variants:i,defaultVariants:n}=r,a=Object.keys(i).map(d=>{const u=t==null?void 0:t[d],s=n==null?void 0:n[d];if(u===null)return null;const g=wr(u)||wr(s);return i[d][g]}),l=t&&Object.entries(t).reduce((d,u)=>{let[s,g]=u;return g===void 0||(d[s]=g),d},{}),c=r==null||(o=r.compoundVariants)===null||o===void 0?void 0:o.reduce((d,u)=>{let{class:s,className:g,...f}=u;return Object.entries(f).every(w=>{let[y,_]=w;return Array.isArray(_)?_.includes({...n,...l}[y]):{...n,...l}[y]===_})?[...d,s,g]:d},[]);return xr(e,a,c,t==null?void 0:t.class,t==null?void 0:t.className)},Pe="-";function ht(e){const r=vt(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;function i(a){const l=a.split(Pe);return l[0]===""&&l.length!==1&&l.shift(),kr(l,r)||bt(a)}function n(a,l){const c=t[a]||[];return l&&o[a]?[...c,...o[a]]:c}return{getClassGroupId:i,getConflictingClassGroupIds:n}}function kr(e,r){var a;if(e.length===0)return r.classGroupId;const t=e[0],o=r.nextPart.get(t),i=o?kr(e.slice(1),o):void 0;if(i)return i;if(r.validators.length===0)return;const n=e.join(Pe);return(a=r.validators.find(({validator:l})=>l(n)))==null?void 0:a.classGroupId}const Cr=/^\[(.+)\]$/;function bt(e){if(Cr.test(e)){const r=Cr.exec(e)[1],t=r==null?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}}function vt(e){const{theme:r,prefix:t}=e,o={nextPart:new Map,validators:[]};return wt(Object.entries(e.classGroups),t).forEach(([n,a])=>{$e(a,o,n,r)}),o}function $e(e,r,t,o){e.forEach(i=>{if(typeof i=="string"){const n=i===""?r:Sr(r,i);n.classGroupId=t;return}if(typeof i=="function"){if(yt(i)){$e(i(o),r,t,o);return}r.validators.push({validator:i,classGroupId:t});return}Object.entries(i).forEach(([n,a])=>{$e(a,Sr(r,n),t,o)})})}function Sr(e,r){let t=e;return r.split(Pe).forEach(o=>{t.nextPart.has(o)||t.nextPart.set(o,{nextPart:new Map,validators:[]}),t=t.nextPart.get(o)}),t}function yt(e){return e.isThemeGetter}function wt(e,r){return r?e.map(([t,o])=>{const i=o.map(n=>typeof n=="string"?r+n:typeof n=="object"?Object.fromEntries(Object.entries(n).map(([a,l])=>[r+a,l])):n);return[t,i]}):e}function xt(e){if(e<1)return{get:()=>{},set:()=>{}};let r=0,t=new Map,o=new Map;function i(n,a){t.set(n,a),r++,r>e&&(r=0,o=t,t=new Map)}return{get(n){let a=t.get(n);if(a!==void 0)return a;if((a=o.get(n))!==void 0)return i(n,a),a},set(n,a){t.has(n)?t.set(n,a):i(n,a)}}}const Nr="!";function kt(e){const{separator:r,experimentalParseClassName:t}=e,o=r.length===1,i=r[0],n=r.length;function a(l){const c=[];let d=0,u=0,s;for(let _=0;_<l.length;_++){let m=l[_];if(d===0){if(m===i&&(o||l.slice(_,_+n)===r)){c.push(l.slice(u,_)),u=_+n;continue}if(m==="/"){s=_;continue}}m==="["?d++:m==="]"&&d--}const g=c.length===0?l:l.substring(u),f=g.startsWith(Nr),w=f?g.substring(1):g,y=s&&s>u?s-u:void 0;return{modifiers:c,hasImportantModifier:f,baseClassName:w,maybePostfixModifierPosition:y}}return t?function(c){return t({className:c,parseClassName:a})}:a}function Ct(e){if(e.length<=1)return e;const r=[];let t=[];return e.forEach(o=>{o[0]==="["?(r.push(...t.sort(),o),t=[]):t.push(o)}),r.push(...t.sort()),r}function St(e){return{cache:xt(e.cacheSize),parseClassName:kt(e),...ht(e)}}const Nt=/\s+/;function zt(e,r){const{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:i}=r,n=new Set;return e.trim().split(Nt).map(a=>{const{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=t(a);let s=!!u,g=o(s?d.substring(0,u):d);if(!g){if(!s)return{isTailwindClass:!1,originalClassName:a};if(g=o(d),!g)return{isTailwindClass:!1,originalClassName:a};s=!1}const f=Ct(l).join(":");return{isTailwindClass:!0,modifierId:c?f+Nr:f,classGroupId:g,originalClassName:a,hasPostfixModifier:s}}).reverse().filter(a=>{if(!a.isTailwindClass)return!0;const{modifierId:l,classGroupId:c,hasPostfixModifier:d}=a,u=l+c;return n.has(u)?!1:(n.add(u),i(c,d).forEach(s=>n.add(l+s)),!0)}).reverse().map(a=>a.originalClassName).join(" ")}function Pt(){let e=0,r,t,o="";for(;e<arguments.length;)(r=arguments[e++])&&(t=zr(r))&&(o&&(o+=" "),o+=t);return o}function zr(e){if(typeof e=="string")return e;let r,t="";for(let o=0;o<e.length;o++)e[o]&&(r=zr(e[o]))&&(t&&(t+=" "),t+=r);return t}function $t(e,...r){let t,o,i,n=a;function a(c){const d=r.reduce((u,s)=>s(u),e());return t=St(d),o=t.cache.get,i=t.cache.set,n=l,l(c)}function l(c){const d=o(c);if(d)return d;const u=zt(c,t);return i(c,u),u}return function(){return n(Pt.apply(null,arguments))}}function x(e){const r=t=>t[e]||[];return r.isThemeGetter=!0,r}const Pr=/^\[(?:([a-z-]+):)?(.+)\]$/i,At=/^\d+\/\d+$/,Mt=new Set(["px","full","screen"]),Et=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Rt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,jt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,It=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Wt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function I(e){return B(e)||Mt.has(e)||At.test(e)}function T(e){return D(e,"length",Ft)}function B(e){return!!e&&!Number.isNaN(Number(e))}function ue(e){return D(e,"number",B)}function K(e){return!!e&&Number.isInteger(Number(e))}function Ut(e){return e.endsWith("%")&&B(e.slice(0,-1))}function h(e){return Pr.test(e)}function L(e){return Et.test(e)}const Tt=new Set(["length","size","percentage"]);function Lt(e){return D(e,Tt,$r)}function Ht(e){return D(e,"position",$r)}const Ot=new Set(["image","url"]);function Vt(e){return D(e,Ot,Gt)}function Bt(e){return D(e,"",Dt)}function Q(){return!0}function D(e,r,t){const o=Pr.exec(e);return o?o[1]?typeof r=="string"?o[1]===r:r.has(o[1]):t(o[2]):!1}function Ft(e){return Rt.test(e)&&!jt.test(e)}function $r(){return!1}function Dt(e){return It.test(e)}function Gt(e){return Wt.test(e)}function Zt(){const e=x("colors"),r=x("spacing"),t=x("blur"),o=x("brightness"),i=x("borderColor"),n=x("borderRadius"),a=x("borderSpacing"),l=x("borderWidth"),c=x("contrast"),d=x("grayscale"),u=x("hueRotate"),s=x("invert"),g=x("gap"),f=x("gradientColorStops"),w=x("gradientColorStopPositions"),y=x("inset"),_=x("margin"),m=x("opacity"),v=x("padding"),C=x("saturate"),z=x("scale"),$=x("sepia"),ee=x("skew"),F=x("space"),G=x("translate"),P=()=>["auto","contain","none"],H=()=>["auto","hidden","clip","visible","scroll"],Ae=()=>["auto",h,r],k=()=>[h,r],jr=()=>["",I,T],fe=()=>["auto",B,h],Ir=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],pe=()=>["solid","dashed","dotted","double","none"],Wr=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Me=()=>["start","end","center","between","around","evenly","stretch"],re=()=>["","0",h],Ur=()=>["auto","avoid","all","avoid-page","page","left","right","column"],te=()=>[B,ue],ge=()=>[B,h];return{cacheSize:500,separator:":",theme:{colors:[Q],spacing:[I,T],blur:["none","",L,h],brightness:te(),borderColor:[e],borderRadius:["none","","full",L,h],borderSpacing:k(),borderWidth:jr(),contrast:te(),grayscale:re(),hueRotate:ge(),invert:re(),gap:k(),gradientColorStops:[e],gradientColorStopPositions:[Ut,T],inset:Ae(),margin:Ae(),opacity:te(),padding:k(),saturate:te(),scale:te(),sepia:re(),skew:ge(),space:k(),translate:k()},classGroups:{aspect:[{aspect:["auto","square","video",h]}],container:["container"],columns:[{columns:[L]}],"break-after":[{"break-after":Ur()}],"break-before":[{"break-before":Ur()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Ir(),h]}],overflow:[{overflow:H()}],"overflow-x":[{"overflow-x":H()}],"overflow-y":[{"overflow-y":H()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",K,h]}],basis:[{basis:Ae()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",h]}],grow:[{grow:re()}],shrink:[{shrink:re()}],order:[{order:["first","last","none",K,h]}],"grid-cols":[{"grid-cols":[Q]}],"col-start-end":[{col:["auto",{span:["full",K,h]},h]}],"col-start":[{"col-start":fe()}],"col-end":[{"col-end":fe()}],"grid-rows":[{"grid-rows":[Q]}],"row-start-end":[{row:["auto",{span:[K,h]},h]}],"row-start":[{"row-start":fe()}],"row-end":[{"row-end":fe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",h]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",h]}],gap:[{gap:[g]}],"gap-x":[{"gap-x":[g]}],"gap-y":[{"gap-y":[g]}],"justify-content":[{justify:["normal",...Me()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Me(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Me(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[_]}],mx:[{mx:[_]}],my:[{my:[_]}],ms:[{ms:[_]}],me:[{me:[_]}],mt:[{mt:[_]}],mr:[{mr:[_]}],mb:[{mb:[_]}],ml:[{ml:[_]}],"space-x":[{"space-x":[F]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[F]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",h,r]}],"min-w":[{"min-w":[h,r,"min","max","fit"]}],"max-w":[{"max-w":[h,r,"none","full","min","max","fit","prose",{screen:[L]},L]}],h:[{h:[h,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[h,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[h,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[h,r,"auto","min","max","fit"]}],"font-size":[{text:["base",L,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ue]}],"font-family":[{font:[Q]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",h]}],"line-clamp":[{"line-clamp":["none",B,ue]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,h]}],"list-image":[{"list-image":["none",h]}],"list-style-type":[{list:["none","disc","decimal",h]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pe(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,T]}],"underline-offset":[{"underline-offset":["auto",I,h]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",h]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",h]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Ir(),Ht]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Lt]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Vt]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...pe(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:pe()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...pe()]}],"outline-offset":[{"outline-offset":[I,h]}],"outline-w":[{outline:[I,T]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:jr()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[I,T]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",L,Bt]}],"shadow-color":[{shadow:[Q]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...Wr(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Wr()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",L,h]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[s]}],saturate:[{saturate:[C]}],sepia:[{sepia:[$]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[s]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[C]}],"backdrop-sepia":[{"backdrop-sepia":[$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",h]}],duration:[{duration:ge()}],ease:[{ease:["linear","in","out","in-out",h]}],delay:[{delay:ge()}],animate:[{animate:["none","spin","ping","pulse","bounce",h]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[z]}],"scale-x":[{"scale-x":[z]}],"scale-y":[{"scale-y":[z]}],rotate:[{rotate:[K,h]}],"translate-x":[{"translate-x":[G]}],"translate-y":[{"translate-y":[G]}],"skew-x":[{"skew-x":[ee]}],"skew-y":[{"skew-y":[ee]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",h]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",h]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",h]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[I,T,ue]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const qt=$t(Zt);function Ar(...e){return qt(A(e))}const Yt=_t("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Mr=de(({className:e,variant:r,size:t,asChild:o=!1,...i},n)=>p(o?vr:"button",{className:Ar(Yt({variant:r,size:t,className:e})),ref:n,...i}));Mr.displayName="Button";const Er=de(({className:e,type:r,...t},o)=>p("input",{type:r,className:Ar("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...t}));Er.displayName="Input";function Xt({className:e}){return p("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",children:p("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"})})}function Jt({widget:e,widgetData:r,className:t="h-[calc(100vh-100px)]",size:o="lg",isDarkMode:i,error:n,disabled:a,canSubmit:l,onTestWidget:c,onClose:d,onReset:u}){const[s]=M(i||!1),[g]=M((e==null?void 0:e.appearance.theme)||"zinc"),[f,w]=M(""),y=Gr(null);function _(m){m.preventDefault(),l&&(w(""),c(f))}return p("div",{className:A(s&&"dark"),children:p("form",{onSubmit:_,className:A(t,"bg-background text-foreground relative flex flex-col overflow-hidden",`theme-${g}`),children:[e&&p("div",{className:A("border-border border-b p-1",o==="full"&&"px-4",o==="lg"&&"px-4",o==="md"&&"px-2",o==="sm"&&"px-2"),children:p("div",{className:"flex items-center justify-between space-x-2",children:[p("div",{children:e&&p("div",{className:"flex items-center space-x-2",children:[e.appearance.logo&&p("img",{src:e.appearance.logo,alt:e.name,className:"h-8 w-8"}),e.name&&p("div",{className:A("text-foreground py-2 text-center font-medium",o==="full"&&"text-base",o==="lg"&&"text-base",o==="md"&&"text-base",o==="sm"&&"text-sm"),children:e.name})]})}),(u||d)&&p("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[u&&p("div",{children:p("button",{type:"button",onClick:u,className:A("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50","hover:bg-accent hover:text-accent-foreground","px-1 py-1"),children:p(Xt,{className:"h-4 w-4"})})}),d&&p("div",{children:p("button",{type:"button",onClick:d,className:A("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50","hover:bg-accent hover:text-accent-foreground","px-1 py-1"),children:p("svg",{className:"h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",children:p("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})})})]})]})}),p("div",{className:A("mx-auto w-full flex-grow overflow-auto px-4 py-2",o!=="full"&&"lg:max-w-[40rem] xl:max-w-[48rem]"),children:p("div",{className:"flex-1 space-y-2",children:[p("div",{className:"relative flex items-center",children:[p(Er,{ref:y,disabled:a,autoFocus:!0,type:"text",name:"message",id:"message",autoComplete:"off",placeholder:'Send "test" or "test-error"',value:f,className:"h-11",onChange:m=>w(m.target.value),required:!0}),p("div",{className:"absolute inset-y-0 right-1 top-1 flex",children:p(Mr,{type:"submit",children:"Test"})})]}),p("div",{className:"text-lg font-bold bg-secondary border border-border rounded-md p-4",children:["DATA: ",JSON.stringify({widgetData:r})]}),n&&p("div",{className:A("mx-auto w-full overflow-auto pb-2",o!=="full"&&"lg:max-w-[40rem] xl:max-w-[48rem]",o==="full"&&"px-4"),children:p(lt,{title:"Error",text:n})})]})})]})})}function Kt({className:e}){return p("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:p("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})}function Qt({widgetId:e,apiUrl:r,disabled:t,options:o,verbose:i}){const[n,a]=M(!0),[l,c]=M(null),[d,u]=M(),[s,g]=M(!0),[f,w]=M(),y=window.location.pathname;Qe(()=>{function v(){let C=`${r}/api/widget/${e}`;u(void 0),fetch(C).then(async z=>{const $=await z.json();console.log({data:$}),c($),a(!1)}).catch(z=>{i&&console.error("[SaasRock.Widget] Error loading widget",{widgetId:e,url:C,error:z.message}),u(d)})}v()},[r,e]);const _=async v=>{const C=new FormData;C.set("action","test-widget"),C.set("content",v),g(!1),u(void 0),fetch(`${r}/api/widget/${e}`,{method:"POST",body:C}).then(async z=>{const $=await z.json();if("error"in $)throw new Error($.error);w($)}).catch(z=>{i&&console.error("[SaasRock.Widget] Error sending message",{widgetId:e,apiUrl:r,error:z}),u(z.message)}).finally(()=>{g(!0)})},m=()=>{w(void 0)};if(n||!l)return null;if(l.appearance.hiddenInUrls.length>0){const v=l.appearance.hiddenInUrls.find(C=>y.startsWith(C));if(v)return i&&console.log("[SaasRock.Widget] Hidden in URL",{hiddenUrl:v,currentPath:y}),null}return l.appearance.visibleInUrls.length>0&&!l.appearance.visibleInUrls.some(C=>y.startsWith(C))?(i&&console.log("[SaasRock.Widget] Not visible in URL",{visibleUrls:l.appearance.visibleInUrls,currentPath:y}),null):l?p(eo,{widget:l,widgetData:f,disabled:t===!0,onTestWidget:_,canSubmit:!t&&s,onReset:t?void 0:m,options:o,error:d}):null}function eo({widget:e,widgetData:r,disabled:t,onTestWidget:o,canSubmit:i,onReset:n,options:a,error:l}){const[c,d]=M(!1),[u,s]=M(!1);return Qe(()=>{(a==null?void 0:a.openDelay)!==void 0&&a.openDelay>=0&&setTimeout(()=>{s(!0)},a.openDelay)},[a==null?void 0:a.openDelay]),c?null:p("div",{className:A("z-50",!(a!=null&&a.isPreview)&&"fixed"),children:[p("button",{type:"button",disabled:t,className:A(!(a!=null&&a.isPreview)&&"fixed",e.appearance.position==="bottom-right"&&"bottom-6 right-6",e.appearance.position==="bottom-left"&&"bottom-6 left-6",e.appearance.position==="top-right"&&"right-6 top-6",e.appearance.position==="top-left"&&"left-6 top-6",e.appearance.position==="center"&&"bottom-6 left-6 right-6",`theme-${e.appearance.theme}`,e.appearance.mode==="dark"&&"dark","bg-primary text-primary-foreground hover:bg-primary/90 shadow","rounded-full shadow-md","flex h-14 w-14 items-center justify-center transition-all duration-300 ease-in-out"),onClick:()=>s(!u),children:e.appearance.logo&&!u?p("img",{src:e.appearance.logo,alt:e.name,className:"h-14 w-14 object-contain"}):p(U,{children:u?p(Kt,{className:"h-5 w-5 transition-all duration-300 ease-in-out"}):p("svg",{className:"h-7 w-7 transition-all duration-300 ease-in-out",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",children:p("path",{"fill-rule":"evenodd",d:"M12 1.5a.75.75 0 0 1 .75.75V4.5a.75.75 0 0 1-1.5 0V2.25A.75.75 0 0 1 12 1.5ZM5.636 4.136a.75.75 0 0 1 1.06 0l1.592 1.591a.75.75 0 0 1-1.061 1.06l-1.591-1.59a.75.75 0 0 1 0-1.061Zm12.728 0a.75.75 0 0 1 0 1.06l-1.591 1.592a.75.75 0 0 1-1.06-1.061l1.59-1.591a.75.75 0 0 1 1.061 0Zm-6.816 4.496a.75.75 0 0 1 .82.311l5.228 7.917a.75.75 0 0 1-.777 1.148l-2.097-.43 1.045 3.9a.75.75 0 0 1-1.45.388l-1.044-3.899-1.601 1.42a.75.75 0 0 1-1.247-.606l.569-9.47a.75.75 0 0 1 .554-.68ZM3 10.5a.75.75 0 0 1 .75-.75H6a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 10.5Zm14.25 0a.75.75 0 0 1 .75-.75h2.25a.75.75 0 0 1 0 1.5H18a.75.75 0 0 1-.75-.75Zm-8.962 3.712a.75.75 0 0 1 0 1.061l-1.591 1.591a.75.75 0 1 1-1.061-1.06l1.591-1.592a.75.75 0 0 1 1.06 0Z","clip-rule":"evenodd"})})})}),u&&p("div",{className:A("fixed",e.appearance.mode==="dark"&&"dark",e.appearance.position==="bottom-right"&&" bottom-24 right-6",e.appearance.position==="bottom-left"&&"bottom-24 left-6",e.appearance.position==="top-right"&&"right-6 top-24",e.appearance.position==="top-left"&&"left-6 top-24",e.appearance.position==="center"&&"left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform","border-border flex w-80 flex-col overflow-hidden rounded-lg border shadow-lg","transition-all duration-700 ease-in-out","h-[calc(100vh-200px)]"),children:p(Jt,{className:"h-[calc(100vh-200px)]",widget:e,widgetData:r,disabled:t||(a==null?void 0:a.isPreview),onTestWidget:o,onReset:n,canSubmit:i,size:"sm",onClose:()=>d(!0),error:l})})]})}function ro(e){let r=e.apiUrl||"http://localhost:3000";return e.widgetId?p("div",{className:A(),children:p("div",{className:"font-sans theme-zinc bg-background text-foreground",children:p(Qt,{widgetId:e.widgetId,apiUrl:r,options:{isPreview:!1,openDelay:e.openDelay},verbose:e.verbose})})}):(console.error("[SaasRock.Widget] Widget ID is required"),null)}const to='*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]{display:none}:root{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--primary: 240 5.9% 10%;--primary-foreground: 0 0% 98%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--ring: 240 10% 3.9%;--radius: .5rem}.dark{--background: 240 10% 3.9%;--foreground: 0 0% 98%;--card: 240 10% 3.9%;--card-foreground: 0 0% 98%;--popover: 240 10% 3.9%;--popover-foreground: 0 0% 98%;--primary: 0 0% 98%;--primary-foreground: 240 5.9% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--muted: 240 3.7% 15.9%;--muted-foreground: 240 5% 64.9%;--accent: 240 3.7% 15.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--ring: 240 4.9% 83.9%}.theme-zinc{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--primary: 222.2 84% 4.9%;--primary-foreground: 0 0% 98%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 240 5.9% 10%;--radius: .5rem}.dark .theme-zinc{--background: 240 10% 3.9%;--foreground: 0 0% 98%;--muted: 240 3.7% 15.9%;--muted-foreground: 240 5% 64.9%;--popover: 240 10% 3.9%;--popover-foreground: 0 0% 98%;--card: 240 10% 3.9%;--card-foreground: 0 0% 98%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--primary: 0 0% 98%;--primary-foreground: 240 5.9% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--accent: 240 3.7% 15.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--ring: 240 4.9% 83.9%}.theme-slate{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--muted: 210 40% 96.1%;--muted-foreground: 215.4 16.3% 46.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--primary: 222.2 47.4% 11.2%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96.1%;--secondary-foreground: 222.2 47.4% 11.2%;--accent: 210 40% 96.1%;--accent-foreground: 222.2 47.4% 11.2%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--ring: 222.2 84% 4.9%;--radius: .5rem}.dark .theme-slate{--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--muted: 217.2 32.6% 17.5%;--muted-foreground: 215 20.2% 65.1%;--popover: 222.2 84% 4.9%;--popover-foreground: 210 40% 98%;--card: 222.2 84% 4.9%;--card-foreground: 210 40% 98%;--border: 217.2 32.6% 17.5%;--input: 217.2 32.6% 17.5%;--primary: 210 40% 98%;--primary-foreground: 222.2 47.4% 11.2%;--secondary: 217.2 32.6% 17.5%;--secondary-foreground: 210 40% 98%;--accent: 217.2 32.6% 17.5%;--accent-foreground: 210 40% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 40% 98%;--ring: 212.7 26.8% 83.9}.theme-stone{--background: 0 0% 100%;--foreground: 20 14.3% 4.1%;--muted: 60 4.8% 95.9%;--muted-foreground: 25 5.3% 44.7%;--popover: 0 0% 100%;--popover-foreground: 20 14.3% 4.1%;--card: 0 0% 100%;--card-foreground: 20 14.3% 4.1%;--border: 20 5.9% 90%;--input: 20 5.9% 90%;--primary: 24 9.8% 10%;--primary-foreground: 60 9.1% 97.8%;--secondary: 60 4.8% 95.9%;--secondary-foreground: 24 9.8% 10%;--accent: 60 4.8% 95.9%;--accent-foreground: 24 9.8% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 60 9.1% 97.8%;--ring: 20 14.3% 4.1%;--radius: .95rem}.dark .theme-stone{--background: 20 14.3% 4.1%;--foreground: 60 9.1% 97.8%;--muted: 12 6.5% 15.1%;--muted-foreground: 24 5.4% 63.9%;--popover: 20 14.3% 4.1%;--popover-foreground: 60 9.1% 97.8%;--card: 20 14.3% 4.1%;--card-foreground: 60 9.1% 97.8%;--border: 12 6.5% 15.1%;--input: 12 6.5% 15.1%;--primary: 60 9.1% 97.8%;--primary-foreground: 24 9.8% 10%;--secondary: 12 6.5% 15.1%;--secondary-foreground: 60 9.1% 97.8%;--accent: 12 6.5% 15.1%;--accent-foreground: 60 9.1% 97.8%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 60 9.1% 97.8%;--ring: 24 5.7% 82.9%}.theme-gray{--background: 0 0% 100%;--foreground: 224 71.4% 4.1%;--muted: 220 14.3% 95.9%;--muted-foreground: 220 8.9% 46.1%;--popover: 0 0% 100%;--popover-foreground: 224 71.4% 4.1%;--card: 0 0% 100%;--card-foreground: 224 71.4% 4.1%;--border: 220 13% 91%;--input: 220 13% 91%;--primary: 220.9 39.3% 11%;--primary-foreground: 210 20% 98%;--secondary: 220 14.3% 95.9%;--secondary-foreground: 220.9 39.3% 11%;--accent: 220 14.3% 95.9%;--accent-foreground: 220.9 39.3% 11%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 20% 98%;--ring: 224 71.4% 4.1%;--radius: .35rem}.dark .theme-gray{--background: 224 71.4% 4.1%;--foreground: 210 20% 98%;--muted: 215 27.9% 16.9%;--muted-foreground: 217.9 10.6% 64.9%;--popover: 224 71.4% 4.1%;--popover-foreground: 210 20% 98%;--card: 224 71.4% 4.1%;--card-foreground: 210 20% 98%;--border: 215 27.9% 16.9%;--input: 215 27.9% 16.9%;--primary: 210 20% 98%;--primary-foreground: 220.9 39.3% 11%;--secondary: 215 27.9% 16.9%;--secondary-foreground: 210 20% 98%;--accent: 215 27.9% 16.9%;--accent-foreground: 210 20% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 20% 98%;--ring: 216 12.2% 83.9%}.theme-neutral{--background: 0 0% 100%;--foreground: 0 0% 3.9%;--muted: 0 0% 96.1%;--muted-foreground: 0 0% 45.1%;--popover: 0 0% 100%;--popover-foreground: 0 0% 3.9%;--card: 0 0% 100%;--card-foreground: 0 0% 3.9%;--border: 0 0% 89.8%;--input: 0 0% 89.8%;--primary: 0 0% 9%;--primary-foreground: 0 0% 98%;--secondary: 0 0% 96.1%;--secondary-foreground: 0 0% 9%;--accent: 0 0% 96.1%;--accent-foreground: 0 0% 9%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 0 0% 3.9%;--radius: .6rem}.dark .theme-neutral{--background: 0 0% 3.9%;--foreground: 0 0% 98%;--muted: 0 0% 14.9%;--muted-foreground: 0 0% 63.9%;--popover: 0 0% 3.9%;--popover-foreground: 0 0% 98%;--card: 0 0% 3.9%;--card-foreground: 0 0% 98%;--border: 0 0% 14.9%;--input: 0 0% 14.9%;--primary: 0 0% 98%;--primary-foreground: 0 0% 9%;--secondary: 0 0% 14.9%;--secondary-foreground: 0 0% 98%;--accent: 0 0% 14.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--ring: 0 0% 83.1%}.theme-red{--background: 0 0% 100%;--foreground: 0 0% 3.9%;--muted: 0 0% 96.1%;--muted-foreground: 0 0% 45.1%;--popover: 0 0% 100%;--popover-foreground: 0 0% 3.9%;--card: 0 0% 100%;--card-foreground: 0 0% 3.9%;--border: 0 0% 89.8%;--input: 0 0% 89.8%;--primary: 0 72.2% 50.6%;--primary-foreground: 0 85.7% 97.3%;--secondary: 0 0% 96.1%;--secondary-foreground: 0 0% 9%;--accent: 0 0% 96.1%;--accent-foreground: 0 0% 9%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 0 72.2% 50.6%;--radius: .4rem}.dark .theme-red{--background: 0 0% 3.9%;--foreground: 0 0% 98%;--muted: 0 0% 14.9%;--muted-foreground: 0 0% 63.9%;--popover: 0 0% 3.9%;--popover-foreground: 0 0% 98%;--card: 0 0% 3.9%;--card-foreground: 0 0% 98%;--border: 0 0% 14.9%;--input: 0 0% 14.9%;--primary: 0 72.2% 50.6%;--primary-foreground: 0 85.7% 97.3%;--secondary: 0 0% 14.9%;--secondary-foreground: 0 0% 98%;--accent: 0 0% 14.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--ring: 0 72.2% 50.6%}.theme-rose{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--primary: 346.8 77.2% 49.8%;--primary-foreground: 355.7 100% 97.3%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 346.8 77.2% 49.8%;--radius: .5rem}.dark .theme-rose{--background: 20 14.3% 4.1%;--foreground: 0 0% 95%;--muted: 0 0% 15%;--muted-foreground: 240 5% 64.9%;--popover: 0 0% 9%;--popover-foreground: 0 0% 95%;--card: 24 9.8% 10%;--card-foreground: 0 0% 95%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--primary: 346.8 77.2% 49.8%;--primary-foreground: 355.7 100% 97.3%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--accent: 12 6.5% 15.1%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 85.7% 97.3%;--ring: 346.8 77.2% 49.8%}.theme-orange{--background: 0 0% 100%;--foreground: 20 14.3% 4.1%;--muted: 60 4.8% 95.9%;--muted-foreground: 25 5.3% 44.7%;--popover: 0 0% 100%;--popover-foreground: 20 14.3% 4.1%;--card: 0 0% 100%;--card-foreground: 20 14.3% 4.1%;--border: 20 5.9% 90%;--input: 20 5.9% 90%;--primary: 24.6 95% 53.1%;--primary-foreground: 60 9.1% 97.8%;--secondary: 60 4.8% 95.9%;--secondary-foreground: 24 9.8% 10%;--accent: 60 4.8% 95.9%;--accent-foreground: 24 9.8% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 60 9.1% 97.8%;--ring: 24.6 95% 53.1%;--radius: .95rem}.dark .theme-orange{--background: 20 14.3% 4.1%;--foreground: 60 9.1% 97.8%;--muted: 12 6.5% 15.1%;--muted-foreground: 24 5.4% 63.9%;--popover: 20 14.3% 4.1%;--popover-foreground: 60 9.1% 97.8%;--card: 20 14.3% 4.1%;--card-foreground: 60 9.1% 97.8%;--border: 12 6.5% 15.1%;--input: 12 6.5% 15.1%;--primary: 20.5 90.2% 48.2%;--primary-foreground: 60 9.1% 97.8%;--secondary: 12 6.5% 15.1%;--secondary-foreground: 60 9.1% 97.8%;--accent: 12 6.5% 15.1%;--accent-foreground: 60 9.1% 97.8%;--destructive: 0 72.2% 50.6%;--destructive-foreground: 60 9.1% 97.8%;--ring: 20.5 90.2% 48.2%}.theme-green{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--primary: 142.1 76.2% 36.3%;--primary-foreground: 355.7 100% 97.3%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 142.1 76.2% 36.3%;--radius: .6rem}.dark .theme-green{--background: 20 14.3% 4.1%;--foreground: 0 0% 95%;--muted: 0 0% 15%;--muted-foreground: 240 5% 64.9%;--popover: 0 0% 9%;--popover-foreground: 0 0% 95%;--card: 24 9.8% 10%;--card-foreground: 0 0% 95%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--primary: 142.1 70.6% 45.3%;--primary-foreground: 144.9 80.4% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--accent: 12 6.5% 15.1%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 85.7% 97.3%;--ring: 142.4 71.8% 29.2%}.theme-blue{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--muted: 210 40% 96.1%;--muted-foreground: 215.4 16.3% 46.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--primary: 221.2 83.2% 53.3%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96.1%;--secondary-foreground: 222.2 47.4% 11.2%;--accent: 210 40% 96.1%;--accent-foreground: 222.2 47.4% 11.2%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--ring: 221.2 83.2% 53.3%;--radius: .6rem}.dark .theme-blue{--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--muted: 217.2 32.6% 17.5%;--muted-foreground: 215 20.2% 65.1%;--popover: 222.2 84% 4.9%;--popover-foreground: 210 40% 98%;--card: 222.2 84% 4.9%;--card-foreground: 210 40% 98%;--border: 217.2 32.6% 17.5%;--input: 217.2 32.6% 17.5%;--primary: 217.2 91.2% 59.8%;--primary-foreground: 222.2 47.4% 11.2%;--secondary: 217.2 32.6% 17.5%;--secondary-foreground: 210 40% 98%;--accent: 217.2 32.6% 17.5%;--accent-foreground: 210 40% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 40% 98%;--ring: 224.3 76.3% 48%}.theme-yellow{--gradient: #faf972;--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--muted: 63 46.5% 21.299999999999997%;--muted-foreground: 63 9.3% 57.1%;--popover: 63 46.4% 9.23%;--popover-foreground: 63 9.3% 98.55%;--card: 63 46.4% 9.23%;--card-foreground: 63 9.3% 98.55%;--border: 63 46.5% 21.299999999999997%;--input: 63 46.5% 21.299999999999997%;--primary: 63 93% 71%;--primary-foreground: 63 9.3% 7.1%;--secondary: 63 46.5% 21.299999999999997%;--secondary-foreground: 63 9.3% 98.55%;--accent: 63 46.5% 21.299999999999997%;--accent-foreground: 63 9.3% 98.55%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 63 9.3% 98.55%;--ring: 63 93% 71%}.dark .theme-yellow{--background: 20 14.3% 4.1%;--foreground: 60 9.1% 97.8%;--muted: 12 6.5% 15.1%;--muted-foreground: 24 5.4% 63.9%;--popover: 20 14.3% 4.1%;--popover-foreground: 60 9.1% 97.8%;--card: 20 14.3% 4.1%;--card-foreground: 60 9.1% 97.8%;--border: 12 6.5% 15.1%;--input: 12 6.5% 15.1%;--primary: 47.9 95.8% 53.1%;--primary-foreground: 26 83.3% 14.1%;--secondary: 12 6.5% 15.1%;--secondary-foreground: 60 9.1% 97.8%;--accent: 12 6.5% 15.1%;--accent-foreground: 60 9.1% 97.8%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 60 9.1% 97.8%;--ring: 35.5 91.7% 32.9%}.theme-violet{--background: 0 0% 100%;--foreground: 224 71.4% 4.1%;--muted: 220 14.3% 95.9%;--muted-foreground: 220 8.9% 46.1%;--popover: 0 0% 100%;--popover-foreground: 224 71.4% 4.1%;--card: 0 0% 100%;--card-foreground: 224 71.4% 4.1%;--border: 220 13% 91%;--input: 220 13% 91%;--primary: 262.1 83.3% 57.8%;--primary-foreground: 210 20% 98%;--secondary: 220 14.3% 95.9%;--secondary-foreground: 220.9 39.3% 11%;--accent: 220 14.3% 95.9%;--accent-foreground: 220.9 39.3% 11%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 20% 98%;--ring: 262.1 83.3% 57.8%;--radius: .6rem}.dark .theme-violet{--background: 224 71.4% 4.1%;--foreground: 210 20% 98%;--muted: 215 27.9% 16.9%;--muted-foreground: 217.9 10.6% 64.9%;--popover: 224 71.4% 4.1%;--popover-foreground: 210 20% 98%;--card: 224 71.4% 4.1%;--card-foreground: 210 20% 98%;--border: 215 27.9% 16.9%;--input: 215 27.9% 16.9%;--primary: 263.4 70% 50.4%;--primary-foreground: 210 20% 98%;--secondary: 215 27.9% 16.9%;--secondary-foreground: 210 20% 98%;--accent: 215 27.9% 16.9%;--accent-foreground: 210 20% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 20% 98%;--ring: 263.4 70% 50.4%}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.\\!visible{visibility:visible!important}.visible{visibility:visible}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-y-0{top:0;bottom:0}.bottom-24{bottom:6rem}.bottom-6{bottom:1.5rem}.left-1\\/2{left:50%}.left-6{left:1.5rem}.right-1{right:.25rem}.right-6{right:1.5rem}.top-1{top:.25rem}.top-1\\/2{top:50%}.top-24{top:6rem}.top-6{top:1.5rem}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.ml-3{margin-left:.75rem}.mt-2{margin-top:.5rem}.inline{display:inline}.flex{display:flex}.inline-flex{display:inline-flex}.hidden{display:none}.h-10{height:2.5rem}.h-11{height:2.75rem}.h-14{height:3.5rem}.h-4{height:1rem}.h-5{height:1.25rem}.h-7{height:1.75rem}.h-8{height:2rem}.h-9{height:2.25rem}.h-\\[calc\\(100vh-100px\\)\\]{height:calc(100vh - 100px)}.h-\\[calc\\(100vh-200px\\)\\]{height:calc(100vh - 200px)}.w-14{width:3.5rem}.w-4{width:1rem}.w-5{width:1.25rem}.w-7{width:1.75rem}.w-8{width:2rem}.w-80{width:20rem}.w-9{width:2.25rem}.w-full{width:100%}.flex-1{flex:1 1 0%}.flex-shrink-0{flex-shrink:0}.flex-grow{flex-grow:1}.-translate-x-1\\/2{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\/2{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.whitespace-nowrap{white-space:nowrap}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.border{border-width:1px}.border-b{border-bottom-width:1px}.border-border{border-color:hsl(var(--border))}.border-input{border-color:hsl(var(--input))}.border-red-300{--tw-border-opacity: 1;border-color:rgb(252 165 165 / var(--tw-border-opacity))}.bg-background{background-color:hsl(var(--background))}.bg-destructive{background-color:hsl(var(--destructive))}.bg-primary{background-color:hsl(var(--primary))}.bg-red-50{--tw-bg-opacity: 1;background-color:rgb(254 242 242 / var(--tw-bg-opacity))}.bg-secondary{background-color:hsl(var(--secondary))}.bg-transparent{background-color:transparent}.object-contain{-o-object-fit:contain;object-fit:contain}.p-1{padding:.25rem}.p-4{padding:1rem}.px-1{padding-left:.25rem;padding-right:.25rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-8{padding-left:2rem;padding-right:2rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.pb-2{padding-bottom:.5rem}.text-center{text-align:center}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.leading-5{line-height:1.25rem}.text-destructive-foreground{color:hsl(var(--destructive-foreground))}.text-foreground{color:hsl(var(--foreground))}.text-primary{color:hsl(var(--primary))}.text-primary-foreground{color:hsl(var(--primary-foreground))}.text-red-400{--tw-text-opacity: 1;color:rgb(248 113 113 / var(--tw-text-opacity))}.text-red-700{--tw-text-opacity: 1;color:rgb(185 28 28 / var(--tw-text-opacity))}.text-red-800{--tw-text-opacity: 1;color:rgb(153 27 27 / var(--tw-text-opacity))}.text-secondary-foreground{color:hsl(var(--secondary-foreground))}.underline-offset-4{text-underline-offset:4px}.shadow{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-sm{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.ring-offset-background{--tw-ring-offset-color: hsl(var(--background))}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-300{transition-duration:.3s}.duration-700{transition-duration:.7s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.file\\:border-0::file-selector-button{border-width:0px}.file\\:bg-transparent::file-selector-button{background-color:transparent}.file\\:text-sm::file-selector-button{font-size:.875rem;line-height:1.25rem}.file\\:font-medium::file-selector-button{font-weight:500}.placeholder\\:text-muted-foreground::-moz-placeholder{color:hsl(var(--muted-foreground))}.placeholder\\:text-muted-foreground::placeholder{color:hsl(var(--muted-foreground))}.hover\\:bg-accent:hover{background-color:hsl(var(--accent))}.hover\\:bg-destructive\\/90:hover{background-color:hsl(var(--destructive) / .9)}.hover\\:bg-primary\\/90:hover{background-color:hsl(var(--primary) / .9)}.hover\\:bg-secondary\\/80:hover{background-color:hsl(var(--secondary) / .8)}.hover\\:text-accent-foreground:hover{color:hsl(var(--accent-foreground))}.hover\\:underline:hover{text-decoration-line:underline}.focus-visible\\:outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\\:ring-1:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\\:ring-2:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\\:ring-ring:focus-visible{--tw-ring-color: hsl(var(--ring))}.focus-visible\\:ring-offset-2:focus-visible{--tw-ring-offset-width: 2px}.disabled\\:pointer-events-none:disabled{pointer-events:none}.disabled\\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\\:opacity-50:disabled{opacity:.5}:is(.dark .dark\\:border-red-700){--tw-border-opacity: 1;border-color:rgb(185 28 28 / var(--tw-border-opacity))}:is(.dark .dark\\:bg-red-900){--tw-bg-opacity: 1;background-color:rgb(127 29 29 / var(--tw-bg-opacity))}:is(.dark .dark\\:text-red-100){--tw-text-opacity: 1;color:rgb(254 226 226 / var(--tw-text-opacity))}:is(.dark .dark\\:text-red-300){--tw-text-opacity: 1;color:rgb(252 165 165 / var(--tw-text-opacity))}@media (min-width: 1024px){.lg\\:max-w-\\[40rem\\]{max-width:40rem}}@media (min-width: 1280px){.xl\\:max-w-\\[48rem\\]{max-width:48rem}}';function oo(e){const r=document.createElement("style");r.textContent=to,e.appendChild(r)}function no(e){const r=document.createElement("div"),t=r.attachShadow({mode:"open"});document.body.appendChild(r),oo(t),Br(p(ro,{...e}),t)}{const e=document.currentScript;if(e){const r=((Rr=e.getAttribute("src"))==null?void 0:Rr.toString())??"",t=new URL(r);console.log({src:r,url:t});const o=e.getAttribute("data-api-url"),i=e.getAttribute("data-widget-id"),n=e.getAttribute("data-verbose")==="true";let a=-1;e.hasAttribute("data-open-delay")&&(a=parseInt(e.getAttribute("data-open-delay")||"-1")),n&&console.log("[SaasRock.Widget] Loading script...",{apiUrl:o,widgetId:i,openDelay:a}),window.Widget={init:l=>{no(l)}},window.Widget.init({apiUrl:o,widgetId:i,verbose:n,openDelay:a})}}});
