import { useEffect, useRef, useState } from "preact/compat";

interface EnrichedInputProps {
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onDomainEnriched?: (data: any) => void;
  onError?: (error: string) => void;
  id?: string;
  name?: string;
  style?: React.CSSProperties;
  autoComplete?: string;
  required?: boolean;
}

export default function EnrichedInput({
  placeholder = "Enter domain",
  className = "",
  disabled = false,
  value: controlledValue,
  onChange,
  onDomainEnriched,
  onError,
  id,
  name,
  style,
  autoComplete,
  required,
}: EnrichedInputProps) {
  const [value, setValue] = useState<string>(controlledValue || "");
  const [loading, setLoading] = useState<boolean>(false);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const inputRef = useRef<HTMLInputElement>(null);

  // Get wizard ID from environment
  const wizardId = import.meta.env.VITE_WIZARD_ID || 'domain-enricher';

  // Domain enrichment function - runs silently in background
  const fetchDomainData = async (domainUrl: string) => {
    if (!domainUrl.trim()) {
      return;
    }

    setLoading(true);

    try {
      // Use the proxy path - Vite will route this to localhost:7071
      const apiResponse = await fetch(`/api/scrapagent?domain_url=${encodeURIComponent(domainUrl)}`);
      
      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status} ${apiResponse.statusText}`);
      }

      const data = await apiResponse.json();
      
      // Call the callback if provided
      if (onDomainEnriched) {
        onDomainEnriched(data);
      }

      console.log('[EnrichedInput] Domain enrichment successful:', data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch domain data";
      console.error('[EnrichedInput] Domain API Error:', err);
      
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debouncing
  const handleInputChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const newValue = target.value;
    
    setValue(newValue);
    
    // Call external onChange if provided
    if (onChange) {
      onChange(newValue);
    }

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer for debounced API call (2 seconds after user stops typing)
    const timer = setTimeout(() => {
      if (newValue.trim()) {
        fetchDomainData(newValue);
      }
    }, 2000); // 2 second delay

    setDebounceTimer(timer);
  };

  const handleBlur = () => {
    // Clear debounce timer and call immediately on blur
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    if (value.trim()) {
      fetchDomainData(value);
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      // Clear debounce timer and call immediately on Enter
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      if (value.trim()) {
        fetchDomainData(value);
      }
    }
  };

  // Update controlled value
  useEffect(() => {
    if (controlledValue !== undefined) {
      setValue(controlledValue);
    }
  }, [controlledValue]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={handleInputChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={`${className} ${loading ? 'pr-10' : ''}`}
        disabled={disabled}
        data-wizard-id={wizardId}
        id={id}
        name={name}
        style={style}
        autoComplete={autoComplete}
        required={required}
      />
      {loading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-600"></div>
        </div>
      )}
    </div>
  );
}

// Export a simple function to create the input with default styling
export function createDomainInput(props: Partial<EnrichedInputProps> = {}) {
  const defaultProps: EnrichedInputProps = {
    placeholder: "Enter domain (e.g., google.com)",
    className: "w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors",
    ...props
  };

  return <EnrichedInput {...defaultProps} />;
}
