import { render } from "preact";
import Widget, { WidgetProps } from "./components/Widget";
import DomainWidget from "./components/DomainWidget";
import tailwindStyles from "./globals.css?inline";

function injectStyles(shadowRoot: ShadowRoot) {
  const style = document.createElement("style");
  style.textContent = tailwindStyles;
  shadowRoot.appendChild(style);
}

// Function to initialize and render the Widget
function initializeWidget(props: WidgetProps) {
  const container = document.createElement("div");
  const shadowRoot = container.attachShadow({ mode: "open" });
  document.body.appendChild(container);
  injectStyles(shadowRoot);
  render(<Widget {...props} />, shadowRoot);
}

// Function to initialize domain widgets on input elements with specific wizard ID
function initializeDomainWidgets() {
  const wizardId = import.meta.env.VITE_WIZARD_ID || 'domain-enricher';
  const inputs = document.querySelectorAll(`input[data-wizard-id="${wizardId}"]`) as NodeListOf<HTMLInputElement>;

  console.log(`[SaasRock.Widget] Looking for inputs with data-wizard-id="${wizardId}", found ${inputs.length}`);

  inputs.forEach(input => {
    if (input.hasAttribute('data-domain-widget-initialized')) {
      return; // Skip if already initialized
    }

    // Mark as initialized to prevent duplicate widgets
    input.setAttribute('data-domain-widget-initialized', 'true');

    console.log(`[SaasRock.Widget] Initializing domain widget for input:`, input);

    // Create container for the domain widget
    const container = document.createElement('div');
    const shadowRoot = container.attachShadow({ mode: 'open' });

    // Position the container relative to the input
    container.style.position = 'absolute';
    container.style.zIndex = '9999';

    // Insert after the input element
    input.parentNode?.insertBefore(container, input.nextSibling);

    // Inject styles
    injectStyles(shadowRoot);

    // Render the domain widget
    render(<DomainWidget input={input} widgetId={wizardId} />, shadowRoot);
  });
}

// Initialize domain widgets when the script loads
function initializeOnLoad() {
  // Initialize domain widgets automatically
  document.addEventListener('DOMContentLoaded', () => {
    initializeDomainWidgets();
  });

  // If DOM is already loaded, initialize immediately
  if (document.readyState === 'loading') {
    // DOM is still loading
  } else {
    // DOM is already loaded
    initializeDomainWidgets();
  }
}

if (import.meta.env.PROD) {
  // Production mode - set up global Widget object
  window.Widget = {
    init: (props: WidgetProps) => {
      initializeWidget(props);
    },
    initDomainWidgets: () => {
      initializeDomainWidgets();
    },
  };

  // Automatically initialize the chat widget if script attributes are present
  const scriptTag = document.currentScript;
  if (scriptTag) {
    const apiUrl = scriptTag.getAttribute("data-api-url");
    const widgetId = scriptTag.getAttribute("data-widget-id");
    const verbose = scriptTag.getAttribute("data-verbose") === "true";
    let openDelay = -1;
    if (scriptTag.hasAttribute("data-open-delay")) {
      openDelay = parseInt(scriptTag.getAttribute("data-open-delay") || "-1");
    }

    if (apiUrl && widgetId) {
      window.Widget.init({ apiUrl, widgetId, verbose, openDelay });
    }
  }

  initializeOnLoad();
} else {
  // Development mode - show the bubble widget
  console.log("[SaasRock.Widget] Loading widget in development mode...");
  const props: WidgetProps = {
    apiUrl: import.meta.env.VITE_API_URL,
    widgetId: import.meta.env.VITE_WIDGET_ID,
    openDelay: 1000,
    verbose: true,
  };
  initializeWidget(props);

  // Also initialize domain widgets for any inputs on the page
  initializeOnLoad();
}

declare global {
  interface Window {
    Widget: {
      init: (props: WidgetProps) => void;
      initDomainWidgets: () => void;
    };
  }
}
