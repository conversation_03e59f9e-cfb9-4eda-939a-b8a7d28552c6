import { render } from "preact";
import Widget, { WidgetProps } from "./components/Widget";
import DomainWidget from "./components/DomainWidget";
import tailwindStyles from "./globals.css?inline";

function injectStyles(shadowRoot: ShadowRoot) {
  const style = document.createElement("style");
  style.textContent = tailwindStyles;
  shadowRoot.appendChild(style);
}

// Function to initialize and render the Widget
function initializeWidget(props: WidgetProps) {
  const container = document.createElement("div");
  const shadowRoot = container.attachShadow({ mode: "open" });
  document.body.appendChild(container);
  injectStyles(shadowRoot);
  render(<Widget {...props} />, shadowRoot);
}

// Function to initialize domain widgets on input elements
function initializeDomainWidgets() {
  const inputs = document.querySelectorAll('input[data-widget-id]') as NodeListOf<HTMLInputElement>;

  inputs.forEach(input => {
    const widgetId = input.getAttribute('data-widget-id');
    if (!widgetId) return;

    // Create container for the domain widget
    const container = document.createElement('div');
    const shadowRoot = container.attachShadow({ mode: 'open' });

    // Position the container relative to the input
    container.style.position = 'absolute';
    container.style.zIndex = '9999';

    // Insert after the input element
    input.parentNode?.insertBefore(container, input.nextSibling);

    // Inject styles
    injectStyles(shadowRoot);

    // Render the domain widget
    render(<DomainWidget input={input} widgetId={widgetId} />, shadowRoot);
  });
}

if (import.meta.env.PROD) {
  // Automatically initialize the chat widget if script attributes are present
  const scriptTag = document.currentScript;
  if (scriptTag) {
    const src = scriptTag.getAttribute("src")?.toString() ?? "";
    const url = new URL(src);
    console.log({ src, url });
    const apiUrl = scriptTag.getAttribute("data-api-url");
    const widgetId = scriptTag.getAttribute("data-widget-id");
    const verbose = scriptTag.getAttribute("data-verbose") === "true";
    let openDelay = -1;
    if (scriptTag.hasAttribute("data-open-delay")) {
      openDelay = parseInt(scriptTag.getAttribute("data-open-delay") || "-1");
    }

    if (verbose) {
      // eslint-disable-next-line no-console
      console.log("[SaasRock.Widget] Loading script...", {
        apiUrl,
        widgetId,
        openDelay,
      });
    }
    window.Widget = {
      init: (props: WidgetProps) => {
        initializeWidget(props);
      },
      initDomainWidgets: () => {
        initializeDomainWidgets();
      },
    };

    window.Widget.init({ apiUrl, widgetId, verbose, openDelay });

    // Also initialize domain widgets automatically
    document.addEventListener('DOMContentLoaded', () => {
      initializeDomainWidgets();
    });

    // If DOM is already loaded, initialize immediately
    if (document.readyState === 'loading') {
      // DOM is still loading
    } else {
      // DOM is already loaded
      initializeDomainWidgets();
    }
  }
} else {
  // eslint-disable-next-line no-console
  console.log("[SaasRock.Widget] Loading widget in development mode...");
  const props: WidgetProps = {
    apiUrl: import.meta.env.VITE_API_URL,
    widgetId: import.meta.env.VITE_WIDGET_ID,
    openDelay: 1000,
    verbose: true,
  };
  initializeWidget(props);
}

declare global {
  interface Window {
    Widget: {
      init: (props: WidgetProps) => void;
      initDomainWidgets: () => void;
    };
  }
}
