import { render } from "preact";
import DomainWidget from "./components/DomainWidget";
import tailwindStyles from "./globals.css?inline";

function injectStyles(shadowRoot: ShadowRoot) {
  const style = document.createElement("style");
  style.textContent = tailwindStyles;
  shadowRoot.appendChild(style);
}

// Function to initialize domain widgets on input elements
function initializeDomainWidgets() {
  const inputs = document.querySelectorAll('input[data-widget-id]') as NodeListOf<HTMLInputElement>;

  inputs.forEach(input => {
    const widgetId = input.getAttribute('data-widget-id');
    if (!widgetId) return;

    // Create container for the domain widget
    const container = document.createElement('div');
    const shadowRoot = container.attachShadow({ mode: 'open' });

    // Position the container relative to the input
    container.style.position = 'absolute';
    container.style.zIndex = '9999';

    // Insert after the input element
    input.parentNode?.insertBefore(container, input.nextSibling);

    // Inject styles
    injectStyles(shadowRoot);

    // Render the domain widget
    render(<DomainWidget input={input} widgetId={widgetId} />, shadowRoot);
  });
}

// Initialize domain widgets when the script loads
function initializeOnLoad() {
  // Initialize domain widgets automatically
  document.addEventListener('DOMContentLoaded', () => {
    initializeDomainWidgets();
  });

  // If DOM is already loaded, initialize immediately
  if (document.readyState === 'loading') {
    // DOM is still loading
  } else {
    // DOM is already loaded
    initializeDomainWidgets();
  }
}

if (import.meta.env.PROD) {
  // Production mode - set up global Widget object
  window.Widget = {
    initDomainWidgets: () => {
      initializeDomainWidgets();
    },
  };

  initializeOnLoad();
} else {
  // Development mode
  console.log("[SaasRock.Widget] Loading domain widget in development mode...");
  initializeOnLoad();
}

declare global {
  interface Window {
    Widget: {
      initDomainWidgets: () => void;
    };
  }
}
