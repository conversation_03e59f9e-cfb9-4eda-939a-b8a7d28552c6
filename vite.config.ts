import { defineConfig } from "vite";
import preact from "@preact/preset-vite";
import path from "path";
import postcss from "rollup-plugin-postcss";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";

export default defineConfig({
  plugins: [
    preact(),
    postcss({
      plugins: [tailwindcss(), autoprefixer()],
      inject: true,
      extract: false,
    }),
    // visualizer({
    //   filename: path.resolve(__dirname, "dist/bundle-analysis.html"),
    //   // open: true,
    //   gzipSize: true,
    //   brotliSize: true,
    // }),
  ],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:7071',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  build: {
    lib: {
      entry: path.resolve(__dirname, "src/main.tsx"),
      name: "SaasRockWidget",
      fileName: () => `embed.js`,
      formats: ["umd"],
    },
    rollupOptions: {
      output: {
        globals: {
          preact: "Preact",
        },
      },
    },
  },
  define: {
    "process.env": {},
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "~": path.resolve(__dirname, "./src"),
      react: "preact/compat",
      "react-dom": "preact/compat",
    },
  },
});
