import { useEffect, useState, useRef } from "preact/compat";
import clsx from "clsx";
import { Input } from "./ui/input";
import ErrorBanner from "./ui/banners/ErrorBanner";

interface Props {
  input: HTMLInputElement;
  widgetId: string;
}

export default function DomainWidget({ input, widgetId }: Props) {
  const [domain, setDomain] = useState<string>(input.value || "");
  const [domainLoading, setDomainLoading] = useState<boolean>(false);
  const [domainResponse, setDomainResponse] = useState<any>(null);
  const [domainError, setDomainError] = useState<string>("");
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Domain enrichment function
  const fetchDomainData = async (domainUrl: string) => {
    if (!domainUrl.trim()) {
      setDomainResponse(null);
      return;
    }

    setDomainLoading(true);
    setDomainError("");

    try {
      // Use the proxy path - Vite will route this to localhost:7071
      const response = await fetch(`/api/scrapagent?domain_url=${encodeURIComponent(domainUrl)}`);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setDomainResponse(data);

    } catch (err) {
      console.error('Domain API Error:', err);
      setDomainError(err instanceof Error ? err.message : "Failed to fetch domain data. Make sure your API at localhost:7071 is running.");
      setDomainResponse(null);
    } finally {
      setDomainLoading(false);
    }
  };

  // Handle domain input change with debouncing
  const handleDomainChange = (value: string) => {
    setDomain(value);
    input.value = value; // Update the original input

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer for debounced API call (1.5 seconds after user stops typing)
    const timer = setTimeout(() => {
      if (value.trim()) {
        fetchDomainData(value);
      } else {
        setDomainResponse(null);
        setDomainError("");
      }
    }, 1500); // 1.5 second delay

    setDebounceTimer(timer);
  };

  const handleDomainBlur = () => {
    // Clear debounce timer and call immediately on blur
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    fetchDomainData(domain);
    
    // Hide the widget after a short delay
    setTimeout(() => {
      setIsVisible(false);
    }, 200);
  };

  const handleDomainKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Clear debounce timer and call immediately on Enter
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      fetchDomainData(domain);
    }
  };

  // Position the widget relative to the input
  const updatePosition = () => {
    if (!containerRef.current) return;
    
    const inputRect = input.getBoundingClientRect();
    const container = containerRef.current;
    
    container.style.position = 'fixed';
    container.style.top = `${inputRect.bottom + 5}px`;
    container.style.left = `${inputRect.left}px`;
    container.style.width = `${Math.max(inputRect.width, 400)}px`;
    container.style.zIndex = '9999';
  };

  // Set up event listeners for the original input
  useEffect(() => {
    const handleInputFocus = () => {
      setIsVisible(true);
      updatePosition();
    };

    const handleInputChange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      handleDomainChange(target.value);
    };

    const handleInputBlur = (e: FocusEvent) => {
      // Check if the blur is moving to our widget
      const relatedTarget = e.relatedTarget as HTMLElement;
      if (relatedTarget && containerRef.current?.contains(relatedTarget)) {
        return; // Don't hide if focus is moving to our widget
      }
      handleDomainBlur();
    };

    const handleInputKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleDomainKeyDown(e as any);
      }
    };

    input.addEventListener('focus', handleInputFocus);
    input.addEventListener('input', handleInputChange);
    input.addEventListener('blur', handleInputBlur);
    input.addEventListener('keydown', handleInputKeyDown);

    // Update position on scroll/resize
    const handlePositionUpdate = () => {
      if (isVisible) {
        updatePosition();
      }
    };

    window.addEventListener('scroll', handlePositionUpdate);
    window.addEventListener('resize', handlePositionUpdate);

    return () => {
      input.removeEventListener('focus', handleInputFocus);
      input.removeEventListener('input', handleInputChange);
      input.removeEventListener('blur', handleInputBlur);
      input.removeEventListener('keydown', handleInputKeyDown);
      window.removeEventListener('scroll', handlePositionUpdate);
      window.removeEventListener('resize', handlePositionUpdate);
      
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [input, isVisible, debounceTimer]);

  // Update position when visibility changes
  useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  if (!isVisible) {
    return null;
  }

  return (
    <div 
      ref={containerRef}
      className="bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-h-96 overflow-auto"
      style={{ fontFamily: 'system-ui, -apple-system, sans-serif' }}
    >
      <div className="space-y-4">
        {domainLoading && (
          <div className="flex justify-end mb-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}

        {domainError && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="text-sm text-red-800">
              <strong>Error:</strong> {domainError}
            </div>
          </div>
        )}

        {domainResponse && (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
            <h4 className="text-xs font-semibold text-gray-700 mb-2">API Response:</h4>
            <pre className="text-xs text-gray-600 bg-white p-2 rounded border overflow-auto max-h-48">
              {JSON.stringify(domainResponse, null, 2)}
            </pre>
          </div>
        )}

        {!domainResponse && !domainError && !domainLoading && domain && (
          <div className="text-sm text-gray-500">
            Enter a domain to see enrichment data...
          </div>
        )}
      </div>
    </div>
  );
}
