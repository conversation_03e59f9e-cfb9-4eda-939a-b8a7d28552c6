<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SaasRock Widget</title>
    <style>
      body {
        font-family: system-ui, -apple-system, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
      }

      input[type="text"] {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
      }

      input[type="text"]:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .test-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
      }
    </style>
  </head>
  <body>
    <h1>SaasRock Widget - Domain Enrichment</h1>

    <div class="test-section">
      <h2>Domain Widget Test</h2>
      <p>The bubble widget will appear in the bottom right corner. Click it to open the domain enrichment interface.</p>
      <p>You can also test domain widgets on input fields below:</p>

      <div class="form-group">
        <label for="domain1">Test Domain Input 1:</label>
        <input
          type="text"
          id="domain1"
          data-widget-id="domain-enricher-1"
          placeholder="Enter domain (e.g., google.com)"
        />
      </div>

      <div class="form-group">
        <label for="domain2">Test Domain Input 2:</label>
        <input
          type="text"
          id="domain2"
          data-widget-id="domain-enricher-2"
          placeholder="Try another domain (e.g., github.com)"
        />
      </div>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
