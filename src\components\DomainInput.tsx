import { useEffect, useRef, useState } from "preact/compat";
import { Input } from "./ui/input";

interface DomainInputProps {
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onDomainEnriched?: (data: any) => void;
  id?: string;
  name?: string;
}

export default function DomainInput({
  placeholder = "Enter domain (e.g., google.com)",
  className,
  disabled = false,
  value: controlledValue,
  onChange,
  onDomainEnriched,
  id,
  name,
}: DomainInputProps) {
  const [value, setValue] = useState<string>(controlledValue || "");
  const [loading, setLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const [showOverlay, setShowOverlay] = useState<boolean>(false);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const inputRef = useRef<HTMLInputElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Get wizard ID from environment
  const wizardId = import.meta.env.VITE_WIZARD_ID || 'domain-enricher';

  // Domain enrichment function
  const fetchDomainData = async (domainUrl: string) => {
    if (!domainUrl.trim()) {
      setResponse(null);
      setShowOverlay(false);
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Use the proxy path - Vite will route this to localhost:7071
      const apiResponse = await fetch(`/api/scrapagent?domain_url=${encodeURIComponent(domainUrl)}`);
      
      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status} ${apiResponse.statusText}`);
      }

      const data = await apiResponse.json();
      setResponse(data);
      setShowOverlay(true);
      
      // Call the callback if provided
      if (onDomainEnriched) {
        onDomainEnriched(data);
      }

    } catch (err) {
      console.error('Domain API Error:', err);
      setError(err instanceof Error ? err.message : "Failed to fetch domain data. Make sure your API at localhost:7071 is running.");
      setResponse(null);
      setShowOverlay(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debouncing
  const handleInputChange = (newValue: string) => {
    setValue(newValue);
    
    // Call external onChange if provided
    if (onChange) {
      onChange(newValue);
    }

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer for debounced API call (1.5 seconds after user stops typing)
    const timer = setTimeout(() => {
      if (newValue.trim()) {
        fetchDomainData(newValue);
      } else {
        setResponse(null);
        setError("");
        setShowOverlay(false);
      }
    }, 1500); // 1.5 second delay

    setDebounceTimer(timer);
  };

  const handleBlur = () => {
    // Clear debounce timer and call immediately on blur
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    if (value.trim()) {
      fetchDomainData(value);
    }
    
    // Hide overlay after a short delay to allow clicking on it
    setTimeout(() => {
      setShowOverlay(false);
    }, 200);
  };

  const handleFocus = () => {
    if (response || error) {
      setShowOverlay(true);
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      // Clear debounce timer and call immediately on Enter
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      if (value.trim()) {
        fetchDomainData(value);
      }
    } else if (e.key === 'Escape') {
      setShowOverlay(false);
    }
  };

  // Position overlay relative to input
  const updateOverlayPosition = () => {
    if (inputRef.current && overlayRef.current) {
      const inputRect = inputRef.current.getBoundingClientRect();
      const overlay = overlayRef.current;
      
      overlay.style.position = 'fixed';
      overlay.style.top = `${inputRect.bottom + 5}px`;
      overlay.style.left = `${inputRect.left}px`;
      overlay.style.width = `${inputRect.width}px`;
      overlay.style.zIndex = '10000';
    }
  };

  // Update controlled value
  useEffect(() => {
    if (controlledValue !== undefined) {
      setValue(controlledValue);
    }
  }, [controlledValue]);

  // Update overlay position when shown
  useEffect(() => {
    if (showOverlay) {
      updateOverlayPosition();
      
      // Update position on scroll/resize
      const handleReposition = () => updateOverlayPosition();
      window.addEventListener('scroll', handleReposition);
      window.addEventListener('resize', handleReposition);
      
      return () => {
        window.removeEventListener('scroll', handleReposition);
        window.removeEventListener('resize', handleReposition);
      };
    }
  }, [showOverlay]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return (
    <div className="relative">
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => handleInputChange(e.target.value)}
          onBlur={handleBlur}
          onFocus={handleFocus}
          onKeyDown={handleKeyDown}
          className={className}
          disabled={disabled || loading}
          data-wizard-id={wizardId}
          id={id}
          name={name}
        />
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {/* Overlay */}
      {showOverlay && (response || error) && (
        <div
          ref={overlayRef}
          className="bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-h-96 overflow-auto"
          style={{ position: 'fixed', zIndex: 10000 }}
        >
          {error && (
            <div className="text-red-600 text-sm mb-2">
              <strong>Error:</strong> {error}
            </div>
          )}
          
          {response && (
            <div>
              <h4 className="text-sm font-semibold text-gray-800 mb-2">Domain Enrichment Data:</h4>
              <pre className="text-xs text-gray-700 bg-gray-100 p-2 rounded border overflow-auto max-h-64">
                {JSON.stringify(response, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
