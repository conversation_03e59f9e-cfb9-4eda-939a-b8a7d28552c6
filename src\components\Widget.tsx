import clsx from "clsx";
import Bubble from "./Bubble";

export interface WidgetProps {
  apiUrl: string | null;
  widgetId: string | null;
  openDelay?: number;
  verbose?: boolean;
}

export default function Widget(props: WidgetProps) {
  let apiUrl = props.apiUrl || "http://localhost:3000";
  if (!props.widgetId) {
    // eslint-disable-next-line no-console
    console.error("[SaasRock.Widget] Widget ID is required");
    return null;
  }

  return (
    <div
      className={
        clsx()
        // "theme-red", "theme-violet", "theme-blue", "theme-green", "theme-orange", "theme-rose", "theme-red", "theme-neutral", "theme-gray", "theme-stone", "theme-slate", "theme-zinc", "theme-none", "theme-yellow"
      }
    >
      <div className="font-sans theme-zinc bg-background text-foreground">
        <Bubble
          widgetId={props.widgetId}
          apiUrl={apiUrl}
          options={{
            isPreview: false,
            openDelay: props.openDelay,
          }}
          verbose={props.verbose}
        />
      </div>
    </div>
  );
}
