import { useState } from "preact/compat";
import DomainInput from "./DomainInput";

export default function DomainInputExample() {
  const [domain1, setDomain1] = useState<string>("");
  const [domain2, setDomain2] = useState<string>("");
  const [enrichmentData, setEnrichmentData] = useState<any>(null);

  const handleDomainEnriched = (data: any) => {
    console.log("Domain enriched:", data);
    setEnrichmentData(data);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Domain Input Component Example</h1>
      
      <div className="space-y-4">
        <div>
          <label htmlFor="domain1" className="block text-sm font-medium text-gray-700 mb-2">
            Domain Input 1 (Controlled):
          </label>
          <DomainInput
            id="domain1"
            value={domain1}
            onChange={setDomain1}
            onDomainEnriched={handleDomainEnriched}
            placeholder="Enter domain (e.g., google.com)"
            className="w-full"
          />
          <p className="text-sm text-gray-500 mt-1">Current value: {domain1}</p>
        </div>

        <div>
          <label htmlFor="domain2" className="block text-sm font-medium text-gray-700 mb-2">
            Domain Input 2 (Uncontrolled):
          </label>
          <DomainInput
            id="domain2"
            onDomainEnriched={handleDomainEnriched}
            placeholder="Try another domain (e.g., github.com)"
            className="w-full"
          />
        </div>

        <div>
          <label htmlFor="regular" className="block text-sm font-medium text-gray-700 mb-2">
            Regular Input (No Domain Enrichment):
          </label>
          <input
            type="text"
            id="regular"
            placeholder="This is a regular input without domain enrichment"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {enrichmentData && (
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Latest Enrichment Data:</h3>
          <pre className="text-sm text-gray-700 bg-white p-3 rounded border overflow-auto max-h-64">
            {JSON.stringify(enrichmentData, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">How it works:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Only inputs with <code className="bg-blue-100 px-1 rounded">data-wizard-id="{import.meta.env.VITE_WIZARD_ID || 'domain-enricher'}"</code> get domain enrichment</li>
          <li>• Type a domain and wait 1.5 seconds, or press Enter, or blur the input to trigger API call</li>
          <li>• The overlay shows the API response below the input</li>
          <li>• Press Escape to hide the overlay</li>
          <li>• API calls are made to <code className="bg-blue-100 px-1 rounded">/api/scrapagent</code> (proxied to localhost:7071)</li>
        </ul>
      </div>
    </div>
  );
}
