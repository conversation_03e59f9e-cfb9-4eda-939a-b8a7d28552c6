{"name": "saasrock-widget", "private": true, "version": "1.3.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:css && vite build", "build:css": "npx tailwindcss -o src/tailwind-output.css --minify", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "typecheck": "tsc -b"}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "@tailwindcss/typography": "^0.5.13", "@types/react": "^18.3.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "openai": "^4.47.1", "postcss-cli": "^11.0.0", "postcss-import": "^16.1.0", "tailwind-merge": "^2.3.0"}, "devDependencies": {"@preact/preset-vite": "^2.8.2", "@types/node": "^20.12.12", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "preact": "^10.22.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "tailwindcss": "^3.3.5", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.2.11", "vite-plugin-css-injected-by-js": "^3.5.1"}}