import { useEffect, useRef } from "preact/compat";
import { render } from "preact";
import DomainWidget from "./DomainWidget";

interface SimpleInputProps {
  placeholder?: string;
  className?: string;
  id?: string;
}

export default function SimpleInput({ 
  placeholder = "Enter domain", 
  className = "w-full px-4 py-2 border border-gray-300 rounded-lg",
  id 
}: SimpleInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const widgetContainerRef = useRef<HTMLDivElement>(null);
  const wizardId = import.meta.env.VITE_WIZARD_ID || 'domain-enricher';

  useEffect(() => {
    if (inputRef.current && widgetContainerRef.current) {
      // Create shadow DOM for the widget
      const shadowRoot = widgetContainerRef.current.attachShadow({ mode: 'open' });
      
      // Inject styles into shadow DOM
      const style = document.createElement('style');
      style.textContent = `
        @import url('https://cdn.tailwindcss.com');
        .widget-container {
          position: absolute;
          z-index: 10000;
          top: 100%;
          left: 0;
          right: 0;
        }
      `;
      shadowRoot.appendChild(style);
      
      // Render the DomainWidget in shadow DOM
      render(
        <DomainWidget 
          input={inputRef.current} 
          widgetId={wizardId} 
        />, 
        shadowRoot
      );
    }
  }, [wizardId]);

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        className={className}
        data-wizard-id={wizardId}
        id={id}
      />
      <div ref={widgetContainerRef} className="relative"></div>
    </div>
  );
}
